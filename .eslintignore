
# Ignoring the test files for the time beinggi
*.test.ts
*.test.tsx
.eslintrc.js
vite.config.mts

# The following files have eslint errors/warnings
src/Pages/GlobalConfigurations/Authorization/APITokens/__tests__/ApiTokens.test.tsx
src/components/AppSelector/ChartSelector.tsx
src/components/ApplicationGroup/AppGroup.service.ts
src/components/ApplicationGroup/AppGroup.utils.ts
src/components/ApplicationGroup/AppGroupAppFilter.components.tsx
src/components/ApplicationGroup/AppGroupDetailsRoute.tsx
src/components/ApplicationGroup/AppGroupRoute.tsx
src/components/ApplicationGroup/Constants.ts
src/components/ApplicationGroup/CreateAppGroup.tsx
src/components/ApplicationGroup/Details/EnvCDDetails/EnvCDDetails.tsx
src/components/ApplicationGroup/Details/EnvCDDetails/__test__/EnvCDDetails.test.tsx
src/components/ApplicationGroup/Details/EnvCIDetails/EnvCIDetails.tsx
src/components/ApplicationGroup/Details/EnvCIDetails/__test__/EnvCIDetails.test.tsx
src/components/ApplicationGroup/Details/EnvironmentConfig/__test__/ApplicationRoutes.test.tsx
src/components/ApplicationGroup/Details/EnvironmentConfig/__test__/EnvConfig.test.tsx
src/components/ApplicationGroup/Details/EnvironmentOverview/HibernateModal.tsx
src/components/ApplicationGroup/Details/EnvironmentOverview/HibernateStatusListDrawer.tsx
src/components/ApplicationGroup/Details/EnvironmentOverview/HibernateStatusRow.tsx
src/components/ApplicationGroup/Details/EnvironmentOverview/__test__/EnvironmentOverview.test.tsx
src/components/ApplicationGroup/Details/TriggerView/BulkCDTrigger.tsx
src/components/ApplicationGroup/Details/TriggerView/BulkCITrigger.tsx
src/components/ApplicationGroup/Details/TriggerView/EnvTriggerView.tsx
src/components/ApplicationGroup/SearchBar.tsx
src/components/CIPipelineN/AdvancedConfigOptions.tsx
src/components/CIPipelineN/Build.tsx
src/components/CIPipelineN/CIPipeline.tsx
src/components/CIPipelineN/CustomImageTags.tsx
src/components/CIPipelineN/CustomInputOutputVariables.tsx
src/components/CIPipelineN/CustomInputVariableSelect.tsx
src/components/CIPipelineN/EnvironmentList.tsx
src/components/CIPipelineN/InputPluginSelect.tsx
src/components/CIPipelineN/MultiplsPort.tsx
src/components/CIPipelineN/OutputDirectoryPath.tsx
src/components/CIPipelineN/PreBuild.tsx
src/components/CIPipelineN/Sidebar.tsx
src/components/CIPipelineN/TaskDetailComponent.tsx
src/components/CIPipelineN/TaskList.tsx
src/components/CIPipelineN/TaskTypeDetailComponent.tsx
src/components/CIPipelineN/VariableContainer.tsx
src/components/CIPipelineN/ciPipeline.utils.tsx
src/components/ClusterNodes/ClusterEvents.tsx
src/components/ClusterNodes/ClusterManifest.tsx
src/components/ClusterNodes/ClusterNodeEmptyStates.tsx
src/components/ClusterNodes/NodeDetails.tsx
src/components/ClusterNodes/__tests__/ClusterManifest.test.tsx
src/components/ClusterNodes/__tests__/NodeList.test.tsx
src/components/Hotjar/Hotjar.tsx
src/components/Jobs/ExpandedRow/ExpandedRow.tsx
src/components/Jobs/JobDetails/JobDetails.tsx
src/components/Jobs/JobList/JobListView.tsx
src/components/Jobs/Jobs.tsx
src/components/Jobs/JobsEmptyState.tsx
src/components/Jobs/Service.ts
src/components/Jobs/Utils.ts
src/components/LogViewer/LogViewer.tsx
src/components/ResourceBrowser/ResourceList/__tests__/Sidebar.test.tsx
src/components/__mocks__/xterm-webfont.js
src/components/app/LogFilter.test.ts
src/components/app/LogFilter.ts
src/components/app/Overview/EnvironmentList.tsx
src/components/app/Overview/Overview.tsx
src/components/app/Overview/TagChipsContainer.tsx
src/components/app/Overview/utils.tsx
src/components/app/ResourceTreeNodes.tsx
src/components/app/ResponsiveDrawer.tsx
src/components/app/WebWorker.ts
src/components/app/create/CreateApp.tsx
src/components/app/create/validationRules.ts
src/components/app/details/AboutAppInfoModal.tsx
src/components/app/details/AboutTagEditModal.tsx
src/components/app/details/appDetails/AppMetrics.tsx
src/components/app/details/appDetails/AppStatusCard.tsx
src/components/app/details/appDetails/DeploymentStatusDetailModal.tsx
src/components/app/details/appDetails/GraphsModal.tsx
src/components/app/details/appDetails/IssuesCard.tsx
src/components/app/details/appDetails/SourceInfo.tsx
src/components/app/details/appDetails/__tests__/GenericInfo.test.tsx
src/components/app/details/appDetails/index.tsx
src/components/app/details/appDetails/utils.tsx
src/components/app/details/cIDetails/CIDetails.tsx
src/components/app/details/cIDetails/cIDetails.util.tsx
src/components/app/details/cdDetails/CDDetails.tsx
src/components/app/details/cicdHistory/History.components.tsx
src/components/app/details/main.tsx
src/components/app/details/metrics/BenchmarkModal.tsx
src/components/app/details/metrics/DeploymentMetrics.tsx
src/components/app/details/metrics/DeploymentTable.tsx
src/components/app/details/metrics/DeploymentTableModal.tsx
src/components/app/details/metrics/deploymentMetrics.service.ts
src/components/app/details/metrics/deploymentMetrics.util.tsx
src/components/app/details/testViewer/List.tsx
src/components/app/details/testViewer/TestRunDetails.tsx
src/components/app/details/testViewer/TestRunList.tsx
src/components/app/details/triggerView/EmptyStateCIMaterial.tsx
src/components/app/details/triggerView/MaterialSource.tsx
src/components/app/details/triggerView/TriggerView.tsx
src/components/app/details/triggerView/__tests__/triggerview.test.tsx
src/components/app/details/triggerView/cdMaterial.tsx
src/components/app/details/triggerView/ciMaterial.tsx
src/components/app/details/triggerView/ciWebhook.service.ts
src/components/app/details/triggerView/config.ts
src/components/app/details/triggerView/workflow.service.ts
src/components/app/details/triggerView/workflow/Workflow.tsx
src/components/app/details/triggerView/workflow/nodes/TriggerExternalCINode.tsx
src/components/app/details/triggerView/workflow/nodes/TriggerLinkedCINode.tsx
src/components/app/details/triggerView/workflow/nodes/staticNode.tsx
src/components/app/details/triggerView/workflow/nodes/triggerCDNode.tsx
src/components/app/details/triggerView/workflow/nodes/triggerCINode.tsx
src/components/app/details/triggerView/workflow/nodes/triggerPrePostCDNode.tsx
src/components/app/details/triggerView/workflow/nodes/workflow.utils.tsx
src/components/app/grepSSEworker.ts
src/components/app/list-new/AppListService.ts
src/components/app/list-new/GenericAppList.tsx
src/components/app/list-new/HelmAppList.tsx
src/components/app/list/TriggerUrl.tsx
src/components/app/list/__tests__/AppListView.test.tsx
src/components/app/list/appList.modal.ts
src/components/app/list/emptyView/Empty.tsx
src/components/app/list/expandedRow/ExpandedRow.tsx
src/components/app/service.ts
src/components/bulkEdits/BulkEdits.tsx
src/components/bulkEdits/bulkedit.utils.tsx
src/components/cdPipeline/BuildCD.tsx
src/components/cdPipeline/CDPipeline.tsx
src/components/cdPipeline/cdPipeline.service.ts
src/components/cdPipeline/cdpipeline.util.tsx
src/components/cdPipeline/validationRules.ts
src/components/chartRepo/ChartRepo.tsx
src/components/chartRepo/chartRepo.service.tsx
src/components/charts/AdvancedConfig.tsx
src/components/charts/ChartGroupAdvanceDeploy.tsx
src/components/charts/ChartGroupDeployments.tsx
src/components/charts/ChartGroupDetails.tsx
src/components/charts/ChartGroupUpdate.tsx
src/components/charts/ChartHeaderFilters.tsx
src/components/charts/Charts.tsx
src/components/charts/MultiChartSummary.tsx
src/components/charts/SavedValues/SavedValuesList.tsx
src/components/charts/chartValues/ChartValues.tsx
src/components/charts/charts.service.ts
src/components/charts/charts.util.tsx
src/components/charts/dialogs/ValuesYamlConfirmDialog.tsx
src/components/charts/discoverChartDetail/About.tsx
src/components/charts/discoverChartDetail/ChartDeploymentList.tsx
src/components/charts/discoverChartDetail/DiscoverChartDetails.tsx
src/components/charts/list/ChartGroup.tsx
src/components/charts/list/ChartListPopUp.tsx
src/components/charts/list/ChartListPopUpRow.tsx
src/components/charts/list/DeployedChartFilters.tsx
src/components/charts/list/DiscoverCharts.tsx
src/components/charts/modal/ChartGroupBasicDeploy.tsx
src/components/charts/modal/CreateChartGroup.tsx
src/components/charts/useChartGroup.ts
src/components/checkList/AllChartsCheck.tsx
src/components/checkList/AllCheckModal.tsx
src/components/checkList/AppCheckList.tsx
src/components/checkList/ChartCheckList.tsx
src/components/checkList/CustomAppDeploy.tsx
src/components/checkList/GlobalAllCheckModal.tsx
src/components/checkList/GlobalChartCheck.tsx
src/components/checkList/SampleAppDeploy.tsx
src/components/checkList/checklist.service.ts
src/components/ciConfig/CIAdvancedConfig.tsx
src/components/ciConfig/CIBuildConfigDiff.tsx
src/components/ciConfig/CIBuildpackBuildOptions.tsx
src/components/ciConfig/CIConfig.tsx
src/components/ciConfig/CIConfig.utils.ts
src/components/ciConfig/CIConfigDiffView.tsx
src/components/ciConfig/CIConfigForm.tsx
src/components/ciConfig/CIContainerRegistryConfig.tsx
src/components/ciConfig/CICreateDockerfileOption.tsx
src/components/ciConfig/CIDockerFileConfig.tsx
src/components/ciConfig/TargetPlatformSelector.tsx
src/components/ciConfig/service.tsx
src/components/ciPipeline/CIPipelineAdvanced.tsx
src/components/ciPipeline/ConfigureWebhook.tsx
src/components/ciPipeline/ExternalCIPipeline.tsx
src/components/ciPipeline/LinkedCIPipelineEdit.tsx
src/components/ciPipeline/LinkedCIPipelineView.tsx
src/components/ciPipeline/Webhook/WebhookDetailsModal.tsx
src/components/ciPipeline/Webhook/webhook.service.ts
src/components/ciPipeline/Webhook/webhook.utils.ts
src/components/ciPipeline/WebhookSelectorCondition.tsx
src/components/ciPipeline/ciPipeline.service.ts
src/components/ciPipeline/validationRules.ts
src/components/cluster/Cluster.tsx
src/components/cluster/ClusterComponentModal.tsx
src/components/cluster/ClusterInfoStepsModal.tsx
src/components/cluster/ClusterInstallStatus.tsx
src/components/cluster/UseNameListDropdown.tsx
src/components/cluster/cluster.service.ts
src/components/cluster/cluster.util.ts
src/components/command/Command.tsx
src/components/command/CommandErrorBoundary.tsx
src/components/command/command.util.ts
src/components/common/Accordian/Accordian.tsx
src/components/common/AppDetailsEmptyState.tsx
src/components/common/Carousel/Carousel.tsx
src/components/common/ContentCard/ContentCard.tsx
src/components/common/Contexts/AppContext.ts
src/components/common/DatePickers/Calender.tsx
src/components/common/DatePickers/DayPickerRangeController.tsx
src/components/common/DeprecatedUpdateWarn.tsx
src/components/common/Description/GenericDescription.tsx
src/components/common/DynamicTabs/__tests__/DynamicTabs.test.tsx
src/components/common/HiddenInput/HiddenInput.tsx
src/components/common/List/List.tsx
src/components/common/MultiSelect/MultiSelect.tsx
src/components/common/Select/Select.tsx
src/components/common/ValidateForm/ValidateForm.tsx
src/components/common/eaEmptyState/EAEmptyState.tsx
src/components/common/edge/rectangularEdge.tsx
src/components/common/edge/straightEdge.tsx
src/components/common/errorBoundary.tsx
src/components/common/formFields/CopyButton.tsx
src/components/common/formFields/DevtronSwitch.tsx
src/components/common/formFields/Typeahead.tsx
src/components/common/helpers/Helpers.tsx
src/components/common/helpers/compareVersion.ts
src/components/common/helpers/isSubset.ts
src/components/common/helpers/time.ts
src/components/common/helpers/utils.tsx
src/components/common/helpers/workflowURL.ts
src/components/common/hooks/FileReader.ts
src/components/common/hooks/__tests__/FileReader.test.tsx
src/components/common/icons/Icons.tsx
src/components/common/lineChart/lineChart.tsx
src/components/common/navigation/Navigation.tsx
src/components/dockerRegistry/Docker.tsx
src/components/dockerRegistry/ManageRegistry.tsx
src/components/dockerRegistry/service.tsx
src/components/external-apps/ExternalAppService.ts
src/components/external-apps/ExternalApps.tsx
src/components/externalArgoApps/ExternalArgoApp.tsx
src/components/externalArgoApps/ExternalArgoAppDetail.tsx
src/components/externalLinks/ExternalLinks.component.tsx
src/components/externalLinks/ExternalLinksCRUD/AddExternalLink.tsx
src/components/externalLinks/ExternalLinksCRUD/ConfigureLinkAction.tsx
src/components/externalLinks/ExternalLinksCRUD/DeleteExternalLinkDialog.tsx
src/components/externalLinks/ExternalLinksCRUD/IdentifierSelector.tsx
src/components/externalLinks/ExternalLinksFilters.tsx
src/components/gitOps/GitOpsConfiguration.tsx
src/components/gitOps/UserGitRepConfiguration.tsx
src/components/gitOps/UserGitRepo.tsx
src/components/gitOps/__tests__/GitOpsConfiguration.test.tsx
src/components/gitOps/gitops.service.tsx
src/components/gitOps/gitops.type.ts
src/components/gitProvider/AddGitHostConfigModal.tsx
src/components/gitProvider/GitProvider.tsx
src/components/gitProvider/gitProvider.service.tsx
src/components/gitProvider/gitProvider.util.tsx
src/components/globalConfigurations/GlobalConfiguration.tsx
src/components/globalConfigurations/GlobalConfigurationProvider.tsx
src/components/hostURL/HostURL.tsx
src/components/hyperion/EnvironmentSelect.tsx
src/components/material/CreateMaterial.tsx
src/components/material/MaterialList.tsx
src/components/material/MaterialView.tsx
src/components/material/UpdateMaterial.tsx
src/components/notifications/AddNotification.tsx
src/components/notifications/ModifyRecipientsModal.tsx
src/components/notifications/NotificationTab.tsx
src/components/notifications/Notifications.tsx
src/components/notifications/notifications.service.ts
src/components/notifications/notifications.util.tsx
src/components/onboardingGuide/GuideCommonHeader.tsx
src/components/onboardingGuide/OnboardingGuide.tsx
src/components/onboardingGuide/onboarding.utils.ts
src/components/project/Project.tsx
src/components/project/ProjectList.tsx
src/components/scopedVariables/Descriptor.tsx
src/components/scopedVariables/DescriptorSearchBar.tsx
src/components/scopedVariables/SavedVariables.tsx
src/components/scopedVariables/ScopedVariables.tsx
src/components/scopedVariables/ScopedVariablesEditor.tsx
src/components/scopedVariables/ScopedVariablesLoader.tsx
src/components/scopedVariables/UploadScopedVariables.tsx
src/components/scopedVariables/VariablesList.tsx
src/components/scopedVariables/__tests__/Descriptor.test.tsx
src/components/scopedVariables/__tests__/DescriptorSearchBar.test.tsx
src/components/scopedVariables/__tests__/SavedVariables.test.tsx
src/components/scopedVariables/__tests__/ScopedVariables.test.tsx
src/components/scopedVariables/__tests__/ScopedVariablesEditor.test.tsx
src/components/scopedVariables/__tests__/ScopedVariablesLoader.test.tsx
src/components/scopedVariables/__tests__/UploadScopedVariables.test.tsx
src/components/scopedVariables/__tests__/VariablesList.test.tsx
src/components/scopedVariables/__tests__/utils.test.tsx
src/components/scopedVariables/utils.tsx
src/components/security/AddCveModal.tsx
src/components/security/DeleteCVEModal.tsx
src/components/security/Security.tsx
src/components/security/SecurityPoliciesTab.tsx
src/components/security/SecurityPolicyApp.tsx
src/components/security/SecurityPolicyCluster.tsx
src/components/security/SecurityPolicyEdit.tsx
src/components/security/SecurityPolicyEnvironment.tsx
src/components/security/SecurityPolicyGlobal.tsx
src/components/security/UpdateSeverityModal.tsx
src/components/security/security.service.ts
src/components/security/security.util.tsx
src/components/terminal/TerminalWrapper.tsx
src/components/util/KeyValueFileInput.tsx
src/components/v2/appDetails/ReleaseStatusEmptyState.tsx
src/components/v2/appDetails/SyncError.component.tsx
src/components/v2/appDetails/__tests__/index.store.test.ts
src/components/v2/appDetails/appDetails.api.ts
src/components/v2/appDetails/ea/EAAppDetail.component.tsx
src/components/v2/appDetails/k8Resource/FilterResource.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/EphemeralContainerDrawer.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/CustomLogsModal/CustomLogsModal.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/Terminal.component.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/terminal/TerminalWrapper.component.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/terminal/__tests__/Terminal.component.test.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/terminal/__tests__/TerminalWrapper.test.tsx
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/terminal/constants.ts
src/components/v2/appDetails/k8Resource/nodeDetail/NodeDetailTabs/terminal/terminal.type.ts
src/components/v2/appDetails/k8Resource/nodeDetail/nodeDetail.api.ts
src/components/v2/appDetails/k8Resource/nodeDetail/nodeDetail.util.tsx
src/components/v2/appDetails/k8Resource/nodeType/NodeTree.component.tsx
src/components/v2/appDetails/k8Resource/nodeType/PodTabSection.tsx
src/components/v2/appDetails/k8Resource/nodeType/__tests__/NodeTree.component.test.ts
src/components/v2/appDetails/k8Resource/nodeType/nodeType.util.ts
src/components/v2/appDetails/k8Resource/nodeType/useNodeTreeReducer.ts
src/components/v2/appDetails/sourceInfo/EnvironmentSelector.component.tsx
src/components/v2/appDetails/sourceInfo/environmentStatus/AppStatusDetailModal.tsx
src/components/v2/appDetails/sourceInfo/environmentStatus/ConfigStatusModal.component.tsx
src/components/v2/appDetails/sourceInfo/environmentStatus/EnvironmentStatus.component.tsx
src/components/v2/appDetails/sourceInfo/environmentStatus/NotesDrawer.tsx
src/components/v2/appDetails/sourceInfo/rotatePods/RotatePodsModal.component.tsx
src/components/v2/appDetails/sourceInfo/rotatePods/RotateResponseModal.tsx
src/components/v2/appDetails/sourceInfo/scaleWorkloads/ScaleWorkloadsModal.component.tsx
src/components/v2/chartDeploymentHistory/ChartDeploymentHistory.component.tsx
src/components/v2/chartDeploymentHistory/DockerListModal.tsx
src/components/v2/chartDeploymentHistory/chartDeploymentHistory.service.ts
src/components/v2/common/ReactSelect.utils.tsx
src/components/v2/common/ReactSelectCustomization.tsx
src/components/v2/devtronStackManager/AboutDevtronView.tsx
src/components/v2/devtronStackManager/DevtronStackManager.component.tsx
src/components/v2/devtronStackManager/DevtronStackManager.service.tsx
src/components/v2/devtronStackManager/DevtronStackManager.tsx
src/components/v2/devtronStackManager/DevtronStackManager.utils.ts
src/components/v2/headers/ChartHeader.component.tsx
src/components/v2/headers/EAHeader.component.tsx
src/components/v2/index.tsx
src/components/v2/utils/tabUtils/useTab.ts
src/components/v2/utils/useSharedState.ts
src/components/v2/values/ChartValues.component.tsx
src/components/v2/values/chartValuesDiff/ChartRepoSelector.tsx
src/components/v2/values/chartValuesDiff/ChartValuesEditor.tsx
src/components/v2/values/chartValuesDiff/ChartValuesView.component.tsx
src/components/v2/values/chartValuesDiff/ChartValuesView.reducer.ts
src/components/v2/values/chartValuesDiff/ChartValuesView.tsx
src/components/v2/values/chartValuesDiff/ProjectUpdateModal.tsx
src/components/v2/values/common/chartValues.api.ts
src/components/workflowEditor/CreateWorkflow.tsx
src/components/workflowEditor/DeprecatedWarningModal.tsx
src/components/workflowEditor/EmptyWorkflow.tsx
src/components/workflowEditor/NoGitOpsConfiguredWarning.tsx
src/components/workflowEditor/PipelineSelect.tsx
src/components/workflowEditor/Workflow.tsx
src/components/workflowEditor/nodes/CDNode.tsx
src/components/workflowEditor/nodes/CINode.tsx
src/components/workflowEditor/nodes/StaticNode.tsx
src/components/workflowEditor/nodes/WebhookNode.tsx
src/components/workflowEditor/nodes/WebhookTippyCard.tsx
src/components/workflowEditor/workflowEditor.tsx
src/config/constants.ts
src/config/routes.ts
src/config/utils.ts
src/index.tsx
src/services/fetchWithFullRoute.ts
src/services/service.ts
src/services/service.types.ts
src/util/MurmurHash3.ts
src/util/Subject.ts
src/util/Util.ts
src/util/__tests__/Subject.test.ts
