{"name": "@devtron-labs/devtron-fe-common-lib", "version": "1.17.0-pre-13", "description": "Supporting common component library", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/devtron-labs/devtron-fe-common-lib.git"}, "author": "Devtron", "license": "ISC", "bugs": {"url": "https://github.com/devtron-labs/devtron-fe-common-lib/issues"}, "homepage": "https://github.com/devtron-labs/devtron-fe-common-lib#readme", "sideEffects": ["**/*.css"], "scripts": {"prepare": "node -e \"try { require('husky').install() } catch (e) {}\"", "lint": "tsc --noEmit && NODE_OPTIONS=--max_old_space_size=3072 eslint 'src/**/*.{js,jsx,ts,tsx}' --max-warnings 0", "lint-fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "test": "exit 0", "dev": "NODE_OPTIONS=--max_old_space_size=3072 vite", "build": "NODE_OPTIONS=--max_old_space_size=3072 vite build --sourcemap", "build-watch": "NODE_OPTIONS=--max_old_space_size=3072 vite build --sourcemap --watch", "build-lib": "vite build", "preview": "vite preview", "lint-staged": "lint-staged", "postinstall": "patch-package", "generate-icon": "node ./scripts/generate-icon.cjs", "generate-illustration": "node ./scripts/generate-illustration.cjs"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "0.2.3", "@laynezh/vite-plugin-lib-assets": "1.1.0", "@sentry/browser": "^7.119.1", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@tippyjs/react": "^4.2.0", "@typeform/embed-react": "2.20.0", "@types/dompurify": "^3.0.5", "@types/json-schema": "^7.0.15", "@types/react": "17.0.39", "@types/react-dom": "17.0.13", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "8.3.0", "@typescript-eslint/parser": "8.3.0", "@vitejs/plugin-react": "4.5.2", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.1.1", "glob": "^10.3.3", "husky": "^7.0.4", "json-schema": "^0.4.0", "lint-staged": "^12.5.0", "moment": "^2.29.4", "prettier": "^3.1.1", "react-ga4": "^1.4.1", "react-toastify": "9.1.3", "sharp": "^0.33.5", "svgo": "^3.3.2", "typescript": "5.5.4", "vite": "6.3.5", "vite-plugin-dts": "4.5.4", "vite-plugin-lib-inject-css": "2.1.1", "vite-plugin-svgr": "^2.4.0", "vite-tsconfig-paths": "5.0.1"}, "peerDependencies": {"@rjsf/core": "^5.13.3", "@rjsf/utils": "^5.13.3", "@rjsf/validator-ajv8": "^5.13.3", "@typeform/embed-react": "2.20.0", "dompurify": "^3.2.4", "patch-package": "^8.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-ga4": "^1.4.1", "react-mde": "^11.5.0", "react-router-dom": "^5.3.0", "react-select": "5.8.0", "rxjs": "^7.8.1", "yaml": "^2.4.1"}, "dependencies": {"@codemirror/autocomplete": "6.18.6", "@codemirror/lang-json": "6.0.1", "@codemirror/lang-yaml": "6.1.2", "@codemirror/language": "6.10.8", "@codemirror/legacy-modes": "6.4.2", "@codemirror/lint": "6.8.4", "@codemirror/merge": "^6.10.0", "@codemirror/search": "6.5.8", "@lezer/highlight": "1.2.1", "@replit/codemirror-indentation-markers": "6.5.3", "@replit/codemirror-vscode-keymap": "6.0.2", "@types/react-dates": "^21.8.6", "@uiw/codemirror-extensions-hyper-link": "4.23.10", "@uiw/codemirror-theme-github": "4.23.7", "@uiw/react-codemirror": "4.23.7", "@xyflow/react": "12.4.2", "ansi_up": "^5.2.1", "codemirror-json-schema": "0.8.0", "dayjs": "^1.11.13", "fast-json-patch": "^3.1.1", "framer-motion": "^6.5.1", "jsonpath-plus": "^10.3.0", "marked": "^13.0.3", "nanoid": "^3.3.8", "qrcode.react": "^4.2.0", "react-canvas-confetti": "^2.0.7", "react-dates": "^21.8.0", "react-draggable": "^4.4.5", "react-international-phone": "^4.5.0", "react-virtualized-sticky-tree": "^3.0.0-beta18", "sass": "^1.69.7", "tslib": "2.7.0"}, "overrides": {"cross-spawn": "^7.0.5", "react-dates": {"react": "^17.0.2", "react-dom": "^17.0.2"}, "vite-plugin-svgr": {"vite": "6.3.5"}, "react-virtualized-sticky-tree": {"react": "^17.0.2", "react-dom": "^17.0.2"}}}