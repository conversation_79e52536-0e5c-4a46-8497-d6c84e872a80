{"name": "dashboard", "version": "1.14.2", "private": true, "homepage": "/dashboard", "dependencies": {"@devtron-labs/devtron-fe-common-lib": "1.17.0-pre-15", "@esbuild-plugins/node-globals-polyfill": "0.2.3", "@rjsf/core": "^5.13.3", "@rjsf/utils": "^5.13.3", "@rjsf/validator-ajv8": "^5.13.3", "@sentry/browser": "7.119.1", "@sentry/integrations": "7.50.0", "@sentry/tracing": "7.50.0", "@tippyjs/react": "4.2.6", "@typeform/embed-react": "2.20.0", "command-line-parser": "^0.2.10", "compute-histogram": "^0.9.11", "dayjs": "^1.11.8", "dompurify": "^3.2.4", "fast-json-patch": "^3.1.1", "flexsearch": "^0.6.32", "jsonpath-plus": "^10.3.0", "moment": "^2.29.4", "query-string": "^7.1.1", "react": "^17.0.2", "react-csv": "^2.2.2", "react-dates": "^21.8.0", "react-dom": "^17.0.2", "react-ga4": "^1.4.1", "react-gtm-module": "^2.0.11", "react-mde": "^11.5.0", "react-router-dom": "^5.3.4", "react-select": "5.8.0", "react-virtualized": "^9.22.5", "recharts": "^2.1.9", "rollup-plugin-node-polyfills": "0.2.1", "rxjs": "^7.5.4", "sockjs-client": "1.6.1", "tippy.js": "^6.3.7", "xterm": "^4.19.0", "xterm-addon-fit": "^0.5.0", "xterm-addon-search": "^0.9.0", "xterm-webfont": "^2.0.0", "yaml": "^2.4.1"}, "scripts": {"prepare": "husky install", "lint": "tsc --noEmit && eslint 'src/**/*.{js,jsx,ts,tsx}' --max-warnings 0", "lint-fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "start": "vite --open", "build": "NODE_OPTIONS=--max_old_space_size=8192 vite build", "serve": "vite preview", "build-light": "NODE_OPTIONS=--max_old_space_size=8192 GENERATE_SOURCEMAP=false vite build", "build-k8s-app": "NODE_OPTIONS=--max_old_space_size=8192 GENERATE_SOURCEMAP=false VITE_K8S_CLIENT=true vite build", "test": "vitest test", "test-coverage:watch": "npm run test -- --coverage", "test-coverage": "npm run test -- --coverage --watchAll=false", "test:ci": "jest --watchAll=false --ci --json --coverage --testLocationInResults --outputFile=report.json", "jest": "jest", "lint-staged": "lint-staged", "postinstall": "patch-package"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "devDependencies": {"@playwright/test": "^1.32.1", "@sentry/cli": "^2.2.0", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@types/jest": "^27.4.1", "@types/node": "20.11.0", "@types/react": "17.0.39", "@types/react-csv": "^1.1.3", "@types/react-dom": "17.0.13", "@types/react-router-dom": "^5.3.3", "@types/react-transition-group": "^4.4.4", "@types/recharts": "^1.8.23", "@types/recompose": "^0.30.10", "@typescript-eslint/eslint-plugin": "8.3.0", "@typescript-eslint/parser": "8.3.0", "@vitejs/plugin-react": "4.5.2", "env-cmd": "10.1.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^7.0.4", "jest-extended": "^2.0.0", "jest-junit": "^13.0.0", "json-schema": "^0.4.0", "lint-staged": "12.5.0", "mock-socket": "^9.2.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.1.1", "react-draggable": "^4.4.5", "react-test-render": "^1.1.2", "sass": "^1.69.7", "sharp": "^0.33.5", "svgo": "^3.3.2", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "5.5.4", "vite": "6.3.5", "vite-plugin-compression2": "2.0.1", "vite-plugin-pwa": "^0.21.1", "vite-plugin-require-transform": "1.0.21", "vite-plugin-svgr": "^2.4.0", "vite-tsconfig-paths": "5.0.1"}, "jest": {"collectCoverageFrom": ["**/*.{ts,tsx,js,jsx}", "!**/?(*.)+(mock).+(ts|js)", "!**/node_modules/**", "!**/coverage/**", "!**/serviceWorker.ts", "!**/index.tsx", "!**/setupProxy.js"], "setupFilesAfterEnv": ["jest-extended"]}, "resolutions": {"nanoid": "^3.3.8", "rollup": "^4.22.4", "path-to-regexp": "^1.9.0", "cross-spawn": "^7.0.5"}, "packageManager": "yarn@4.9.2"}