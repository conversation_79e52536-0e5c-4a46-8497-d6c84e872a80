{"name": "@devtron-labs/devtron-fe-lib", "version": "1.16.0", "description": "Supporting component library", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/devtron-labs/devtron-fe-lib.git"}, "author": "Devtron", "license": "ISC", "bugs": {"url": "https://github.com/devtron-labs/devtron-fe-lib/issues"}, "homepage": "https://github.com/devtron-labs/devtron-fe-lib#readme", "sideEffects": ["**/*.css"], "scripts": {"prepare": "node -e \"try { require('husky').install() } catch (e) {}\"", "test": "echo \"Error: no test specified\" && exit 1", "dev": "vite", "build": "vite build --sourcemap", "build-watch": "vite build --sourcemap --watch", "build-lib": "vite build", "preview": "vite preview", "lint": "tsc --noEmit && eslint 'src/**/*.{js,jsx,ts,tsx}' --max-warnings 0", "lint-fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "lint-staged": "lint-staged", "postinstall": "patch-package"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "0.2.3", "@laynezh/vite-plugin-lib-assets": "1.1.0", "@sentry/browser": "^7.119.1", "@tippyjs/react": "^4.2.0", "@types/json-schema": "^7.0.15", "@types/react": "17.0.39", "@types/react-dom": "17.0.13", "@types/react-grid-layout": "^1.3.5", "@types/react-router-dom": "^5.3.3", "@types/react-virtualized": "^9.21.30", "@typescript-eslint/eslint-plugin": "8.3.0", "@typescript-eslint/parser": "8.3.0", "@vitejs/plugin-react": "4.5.2", "dayjs": "^1.11.8", "dompurify": "^3.2.4", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.1.1", "glob": "^10.3.3", "husky": "^7.0.4", "lint-staged": "^12.5.0", "moment": "^2.30.1", "prettier": "^3.1.1", "sharp": "^0.33.5", "svgo": "^3.3.2", "typescript": "5.5.4", "vite": "6.3.5", "vite-plugin-dts": "4.5.4", "vite-plugin-lib-inject-css": "2.0.1", "vite-plugin-svgr": "^2.4.0", "vite-tsconfig-paths": "5.0.1"}, "peerDependencies": {"@devtron-labs/devtron-fe-common-lib": "1.17.0-pre-12", "patch-package": "^8.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^5.3.0", "react-select": "5.8.0", "yaml": "^2.4.1"}, "dependencies": {"fast-json-patch": "^3.1.1", "jsonpath-plus": "^10.3.0", "moment-timezone": "^0.5.45", "react-ga4": "^1.4.1", "react-grid-layout": "^1.5.0", "sass": "^1.69.7", "tslib": "2.7.0"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "overrides": {"cross-spawn": "^7.0.5", "nanoid": "^3.3.8", "vite-plugin-svgr": {"vite": "6.3.5"}, "vite": {"rollup": "4.22.4"}}}