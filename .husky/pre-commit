#!/bin/sh
#
# Copyright (c) 2024. Devtron Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

. "$(dirname "$0")/_/husky.sh"

echo "Running pre-commit hook..."

# Check for changes in the Icon folder
ICON_CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACMR | grep -E 'IconV2/|generate-icon.cjs' || true)

if [ -n "$ICON_CHANGED_FILES" ]; then
    echo "Changes detected in the Icon folder or icon generation script. Running icon generation script..."
    
    if ! npm run generate-icon; then
        echo "Error: Icon generation script failed."
        exit 1
    fi
    
    echo "Icon.tsx updated. Adding to commit."
    git add src/Shared/Components/Icon/Icon.tsx
else
    echo "No changes in the IconsV2 folder. Skipping icon generation."
fi

# Check for changes in the Illustration folder
ILLUSTRATION_CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACMR | grep -E 'Illustration/|generate-illustration.cjs' || true)

if [ -n "$ILLUSTRATION_CHANGED_FILES" ]; then
    echo "Changes detected in the Illustration folder or illustration generation script. Running illustration generation script..."
    
    if ! npm run generate-illustration; then
        echo "Error: Illustration generation script failed."
        exit 1
    fi
    
    echo "Illustration.tsx updated. Adding to commit."
    git add src/Shared/Components/Illustration/Illustration.tsx
else
    echo "No changes in the Illustration folder. Skipping illustration generation."
fi

# TypeScript check
if ! npx tsc --noEmit; then
    echo "Error: TypeScript check failed."
    exit 1
fi

# Lint-staged
if ! npm run lint-staged; then
    echo "Error: Lint-staged failed."
    exit 1
fi

echo "Pre-commit hook completed successfully."
