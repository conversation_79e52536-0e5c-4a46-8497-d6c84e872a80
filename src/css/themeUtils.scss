/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@use './mixins.scss' as *;

.bg {
    &__primary {
        background: var(--bg-primary);
    }

    &__secondary {
        background: var(--bg-secondary);
    }

    &__tertiary {
        background: var(--bg-tertiary);
    }

    &__overlay {
        &--primary {
            background: var(--bg-overlay-primary);
        }

        &--secondary {
            background: var(--bg-overlay-secondary);
        }
    }

    &__menu {
        &--primary {
            background: var(--bg-menu-primary);
        }

        &--secondary {
            background: var(--bg-menu-secondary);
        }
    }

    &__modal {
        &--primary {
            background: var(--bg-modal-primary);
        }

        &--secondary {
            background: var(--bg-modal-secondary);
        }

        &--tertiary {
            background: var(--bg-modal-tertiary);
        }
    }

    &__hover {
        &:hover {
            background: var(--bg-hover);
        }

        &--opaque:hover {
            background: var(--bg-hover-opaque);
        }

        &-secondary {
            &--opaque:hover {
                background: var(--bg-hover-secondary-opaque);
            }
        }
    }

    &__white {
        background: var(--white);
    }
}

.text {
    &__white {
        color: var(--white);
    }

    &__black {
        color: var(--black);

        &--imp {
            color: var(--black) !important;
        }
    }
}

.icon-fill {
    &__white {
        *[fill^="#"] {
            fill: var(--white);
        }
    }
}

.icon-stroke {
    &__white {
        *[stroke^="#"] {
            stroke: var(--white);
        }
    }
}

.shadow {
    &__menu {
        box-shadow: var(--shadow-menu);
    }

    &__modal {
        box-shadow: var(--shadow-modal);
    }

    &__overlay {
        box-shadow: var(--shadow-overlay);
    }

    &__drawer {
        box-shadow: var(--shadow-drawer);
    }

    &__key {
        box-shadow: var(--shadow-key);
    }

    &__card {
        &--10 {
            box-shadow: var(--shadow-10);
        }

        &--20 {
            box-shadow: var(--shadow-20);
        }

        &--30 {
            box-shadow: var(--shadow-30);
        }
    }
}

.border {
    @include border("primary", "--border-primary");
    @include border("primary-translucent", "--border-primary-translucent");
    @include border("secondary", "--border-secondary");
    @include border("secondary-translucent", "--border-secondary-translucent");
    @include border("white", "--white");
    @include border("white-10", "--white-10");
    @include border("primary-warning", "--Y200");
}

.divider {
    @include divider("primary", "--divider-primary");
    @include divider("primary-translucent", "--divider-primary-translucent");
    @include divider("secondary", "--divider-secondary");
    @include divider("secondary-translucent", "--divider-secondary-translucent");
}