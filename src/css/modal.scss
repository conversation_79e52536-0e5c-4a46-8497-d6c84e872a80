/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.modal__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.modal__body {
    display: block;
    width: 400px;
    background-color: var(--bg-primary);
    opacity: 1;
    margin: auto;
    padding: 20px;
    border-radius: 8px;
    border: solid 1px transparent;
    margin-top: 84px;
}

.modal__body.modal__body--no-padding {
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
}

.modal__body--w-600 {
    width: 600px;
}

.modal__title {
    font-size: 20px;
    font-weight: 600;
    line-height: 1.33;
    color: var(--N900);
    margin-top: 0px;
    margin-bottom: 0px;
}

.visible-modal__body {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
}

.confirmation-dialog__body {
    margin: 0 auto;
    width: 450px;
    padding: 24px;
    background: var(--bg-primary);
    border-radius: 8px;
    margin-top: 40px;
    position: relative;
}

.confirmation-dialog__body--w-400 {
    width: 400px;
}

.confirmation-dialog__icon {
    width: 48px;
    margin-bottom: 16px;
}

.confirmation-dialog__title {
    font-size: 16px;
    margin-top: 0;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--N900);
}

.confirmation-dialog__subtitle {
    font-size: 13px;
    color: var(--N800);
    line-height: 20px;
}

.confirmation-dialog__button-group {
    margin-top: 32px;
    button {
        margin-left: 12px;
    }
}

.modal__body-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 24px;
}

.modal__body-bottom {
    border-top: solid 1px var(--N200);
}

.modal__main-img {
    margin-bottom: 16px;
    width: 48px;
    height: 48px;
}

.cta-cd-delete-modal {
    min-width: 76px !important;
}

.drawer--container {
    display: flex;
    overflow: hidden;
}

.modal__body--p-0 {
    padding: 0px;
}
