/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* Contains icon classes */

.healthy,
.deployed,
.synced,
.sync.ok,
.superseded,
.deploy-success {
    background-image: url('../assets/icons/appstatus/healthy.svg');
}

.missing {
    background-image: url('../assets/icons/appstatus/missing.svg');
}

.unknown,
.wf-unknown {
    background-image: url('../assets/icons/appstatus/unknown.svg');
}

.not-deployed {
    background-image: url('../assets/icons/appstatus/notdeployed.svg');
}

.queued {
    background-image: url('../assets/icons/ic-clock.svg');
}

.suspended {
    background-image: url('../assets/icons/appstatus/suspended.svg');
}

.degraded,
.outofsync,
.sync.failed {
    background-image: url('../assets/icons/appstatus/degraded.svg');
}

.progressing,
.uninstalling,
.pending-install,
.pending-upgrade,
.pending-rollback,
.deploy-init,
.request-accepted,
.git-success,
.acd-success {
    background-image: url('../assets/icons/appstatus/progressing-rotating.svg');
}

.enqueued,
.hibernating {
    background-image: url('../assets/icons/ic-hibernate-2.svg');
}

.failed {
    background-image: url('../assets/icons/ic-failure.svg');
}

.unabletofetch {
    background-image: url('../assets/icons/appstatus/ic-appstatus-failed.svg');
}

.timed-out {
    background-image: url('../assets/icons/ic-timeout-red.svg');
}

.outofsync,
.sync.failed,
.que-error,
.deque-error,
.git-error,
.acd-error,
.trigger-error {
    background-image: url('../assets/icons/misc/errorInfo.svg');
    transform: rotate(180deg);
}

.icon-dim-6 {
    width: 6px;
    height: 6px;
}

.icon-dim-8 {
    width: 8px;
    height: 8px;
}

.icon-dim-8-imp {
    width: 8px !important;
    height: 8px !important;
}

.icon-dim-10 {
    width: 10px;
    height: 10px;
}

.icon-dim-12 {
    width: 12px;
    height: 12px;
}

.icon-dim-13 {
    width: 13px;
    height: 13px;
}

.icon-dim-14 {
    width: 14px;
    height: 14px;
}

.icon-dim-16 {
    width: 16px;
    height: 16px;
}

.icon-dim-16-imp {
    width: 16px !important;
    height: 16px !important;
}

.icon-dim-18 {
    width: 18px;
    height: 18px;
}

.icon-dim-20 {
    width: 20px;
    height: 20px;
}

.icon-dim-20-imp {
    width: 20px !important;
    height: 20px !important;
}

.icon-dim-22 {
    width: 22px;
    height: 22px;
}

.icon-dim-24 {
    width: 24px;
    height: 24px;
}

.icon-dim-28 {
    width: 28px;
    height: 28px;
}

.icon-dim-30 {
    width: 30px;
    height: 30px;
}

.icon-dim-32 {
    width: 32px;
    height: 32px;
}

.icon-dim-34 {
    width: 34px;
    height: 34px;
}

.icon-dim-36 {
    width: 36px;
    height: 36px;
}

.icon-dim-40 {
    width: 40px;
    height: 40px;
}

.icon-dim-42 {
    width: 42px;
    height: 42px;
}

.icon-dim-44 {
    width: 44px;
    height: 44px;
}

.icon-dim-48 {
    width: 48px;
    height: 48px;
}

.icon-dim-50 {
    width: 50px;
    height: 50px;
}

.icon-dim-72 {
    width: 72px;
    height: 72px;
}

.icon-dim-80 {
    width: 80px;
    height: 80px;
}

.icon-n2 {
    .fill-color {
        fill: var(--N200);
    }

    .stroke-color {
        stroke: var(--N200);
    }
}

.icon-n3 {
    .fill-color {
        fill: var(--N300);
    }

    .stroke-color {
        stroke: var(--N300);
    }
}

.icon-n4 {
    .fill-color {
        fill: var(--N400);
    }

    .stroke-color {
        stroke: var(--N400);
    }
}

.icon-n5 {
    .fill-color {
        fill: var(--N500);
    }

    .stroke-color {
        stroke: var(--N500);
    }
}

.icon-n6 {
    .fill-color {
        fill: var(--N600);
    }

    .stroke-color {
        stroke: var(--N600);
    }
}

.icon-fill-n6 {
    .fill-color {
        fill: var(--N600);
    }
}

.icon-use-fill-n6 {
    use {
        fill: var(--N600);
    }
}

.icon-delete {
    &:hover {
        .stroke-color {
            stroke: var(--R500);
        }
    }

    // For delete interactive icon
    &__hover {
        path {
            stroke: var(--N600);
        }

        &:hover {
            path {
                stroke: var(--R500);
            }
        }
    }
}

.dc__hover-remove-btn {
    @extend .icon-use-fill-n6;

    &:hover {
        background: var(--R100);
        border-radius: 2px;

        svg use {
            fill: var(--R500);
        }
    }
}

@mixin dot-icon-size($size) {
    width: calc(0.625 * #{$size}px);
    height: calc(0.625 * #{$size}px);

    > circle {
        stroke-width: calc(0.125 * #{$size}px);
    }
}

@each $size in 14, 16, 18, 20, 22, 24, 30, 32, 48 {
    .icon-dot-#{$size} {
        position: relative;
        display: flex;

        & > svg:first-child {
            width: #{$size}px;
            height: #{$size}px;
        }

        &:not(.visible) > svg:last-child {
            display: none;
        }

        &.visible > svg:last-child {
            position: absolute;
            top: 0;
            right: 0;
            transform: translate(50%, -50%);
            @include dot-icon-size($size);
        }
    }
}
