/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

$white: 255, 255, 255;
$black: 0, 0, 0;

@mixin theme-light {
    $B50: 240, 247, 255;
    $N100: 237, 241, 245;

    --V700: rgb(73, 52, 179);
    --V500: rgb(102, 75, 238);
    --V200: rgb(182, 171, 243);
    --V300: rgb(163, 147, 245);
    --V100: rgb(243, 240, 255);
    --B700: rgb(0, 57, 116);
    --B600: rgb(0, 79, 161);
    --B500: rgb(0, 102, 204);
    --B400: rgb(57, 137, 217);
    --B300: rgb(114, 172, 230);
    --B200: rgb(171, 207, 243);
    --B100: rgb(229, 242, 255);
    --B50: rgb(#{$B50});
    --N900: rgb(0, 10, 20);
    --N800: rgb(29, 39, 48);
    --N700: rgb(59, 68, 76);
    --N600: rgb(89, 97, 104);
    --N500: rgb(118, 125, 132);
    --N400: rgb(149, 155, 161);
    --N300: rgb(177, 183, 188);
    --N200: rgb(208, 212, 217);
    --N100: rgb(#{$N100});
    --N50: rgb(247, 250, 252);
    --N0: rgb(#{$white});
    --G700: rgb(16, 98, 64);
    --G600: rgb(22, 136, 89);
    --G500: rgb(29, 173, 112);
    --G400: rgb(80, 193, 145);
    --G300: rgb(131, 213, 178);
    --G200: rgb(182, 233, 211);
    --G100: rgb(233, 251, 244);
    --G50: rgb(242, 255, 250);
    --Y800: rgb(89, 67, 32);
    --Y700: rgb(140, 99, 36);
    --Y600: rgb(197, 141, 54);
    --Y500: rgb(255, 181, 73);
    --Y400: rgb(255, 197, 112);
    --Y300: rgb(255, 213, 151);
    --Y200: rgb(255, 229, 190);
    --Y100: rgb(255, 245, 229);
    --Y50: rgb(255, 248, 237);
    --R700: rgb(178, 18, 18);
    --R600: rgb(199, 50, 50);
    --R500: rgb(243, 62, 62);
    --R400: rgb(246, 104, 104);
    --R300: rgb(249, 146, 146);
    --R200: rgb(252, 188, 188);
    --R100: rgb(253, 231, 231);
    --R50: rgb(253, 240, 240);
    --O700: rgb(178, 52, 18);
    --O600: rgb(229, 94, 57);
    --O500: rgb(255, 126, 91);
    --O200: rgb(255, 190, 173);
    --O100: rgb(255, 229, 222);
    --O50: rgb(252, 243, 240);

    // Custom colors
    --bg-pipeline-trigger-type: rgb(165, 167, 191);
    --bg-bulk-selection-widget: rgba(#{$B50}, 0.72);

    // Color Tokens
    --bg-primary: var(--N0);
    --bg-secondary: var(--N50);
    --bg-tertiary: rgb(242, 244, 247);

    --bg-overlay-primary: var(--bg-primary);
    --bg-overlay-secondary: var(--bg-secondary);

    --bg-menu-primary: var(--bg-primary);
    --bg-menu-secondary: var(--bg-secondary);

    --bg-modal-primary: var(--bg-primary);
    --bg-modal-secondary: var(--bg-secondary);
    --bg-modal-tertiary: var(--bg-tertiary);

    --bg-tooltip-black: var(--N900);
    --bg-toast: var(--N800);
    --bg-sidebar: var(--B500);
    --bg-sidebar-modern-layout: var(--B500);
    --bg-sidebar-item-selected: rgba(#{$black}, 0.35);
    --bg-shimmer-loader: linear-gradient(90deg, var(--N200) 0%, var(--N50) 50%, var(--N200) 80%);
    --bg-hover: rgba(64, 96, 128, 0.05);
    --bg-hover-opaque: rgb(245, 247, 249);
    --bg-hover-secondary-opaque: rgb(238, 242, 246);
    --bg-body: linear-gradient(90deg, var(--B500) 0%, var(--B500) 10%, rgb(0, 82, 165) 25%, rgb(0, 47, 94) 50%, rgb(0, 33, 65) 100%);
    --bg-segmented-control: rgba(#{$black}, 0.03);

    --backdrop-bg: rgba(#{$black}, 0.75);

    --border-primary: var(--N200);
    --border-primary-translucent: rgba(#{$black}, 0.18);
    --border-secondary: var(--N100);
    --border-secondary-translucent: rgba(#{$black}, 0.06);

    --divider-primary: var(--border-primary);
    --divider-primary-translucent: var(--border-primary-translucent);
    --divider-secondary: var(--border-secondary);
    --divider-secondary-translucent: var(--border-secondary-translucent);
}

@mixin theme-dark {
    color-scheme: dark;

    $N0: 21, 22, 31;

    --V700: rgb(202, 191, 255);
    --V500: rgb(166, 149, 255);
    --V200: rgb(79, 73, 121);
    --V300: rgb(108, 98, 165);
    --V100: rgb(50, 47, 76);
    --B700: rgb(172, 211, 255);
    --B600: rgb(144, 196, 255);
    --B500: rgb(116, 181, 255);
    --B400: rgb(97, 149, 210);
    --B300: rgb(78, 118, 165);
    --B200: rgb(59, 86, 121);
    --B100: rgb(40, 54, 76);
    --B50: rgb(30, 38, 53);
    --N900: rgb(248, 248, 249);
    --N800: rgb(228, 229, 230);
    --N700: rgb(200, 203, 206);
    --N600: rgb(159, 164, 169);
    --N500: rgb(118, 125, 132);
    --N400: rgb(99, 104, 112);
    --N300: rgb(79, 84, 92);
    --N200: rgb(60, 63, 71);
    --N100: rgb(40, 43, 51);
    --N50: rgb(31, 32, 41);
    --N0: rgb(#{$N0});
    --G700: rgb(175, 221, 179);
    --G600: rgb(149, 209, 153);
    --G500: rgb(122, 198, 128);
    --G400: rgb(102, 163, 109);
    --G300: rgb(82, 128, 89);
    --G200: rgb(61, 92, 70);
    --G100: rgb(41, 57, 50);
    --G50: rgb(33, 43, 43);
    --Y800: rgb(251, 227, 193);
    --Y700: rgb(248, 214, 161);
    --Y600: rgb(246, 200, 130);
    --Y500: rgb(244, 186, 99);
    --Y400: rgb(199, 153, 85);
    --Y300: rgb(155, 120, 72);
    --Y200: rgb(110, 88, 56);
    --Y100: rgb(66, 55, 45);
    --Y50: rgb(43, 38, 38);
    --R700: rgb(255, 158, 160);
    --R600: rgb(255, 125, 128);
    --R500: rgb(255, 93, 96);
    --R400: rgb(208, 79, 83);
    --R300: rgb(161, 65, 70);
    --R200: rgb(115, 50, 57);
    --R100: rgb(68, 36, 44);
    --R50: rgb(49, 31, 39);
    --O700: rgb(255, 178, 157);
    --O600: rgb(255, 152, 124);
    --O500: rgb(255, 126, 91);
    --O200: rgb(115, 64, 55);
    --O100: rgb(68, 43, 43);
    --O50: rgb(44, 32, 37);

    // Custom colors
    --bg-pipeline-trigger-type: rgb(68, 70, 90);
    --bg-bulk-selection-widget: rgba(20, 28, 38, 0.72);

    // Color Tokens
    --bg-primary: rgb(30, 31, 40);
    --bg-secondary: rgb(26, 27, 35);
    --bg-tertiary: rgb(#{$N0});

    --bg-overlay-primary: rgb(49, 50, 58);
    --bg-overlay-secondary: rgb(40, 41, 49);

    --bg-menu-primary: var(--bg-overlay-secondary);
    --bg-menu-secondary: var(--bg-primary);

    --bg-modal-primary: var(--bg-primary);
    --bg-modal-secondary: var(--bg-secondary);
    --bg-modal-tertiary: var(--bg-tertiary);

    --bg-tooltip-black: rgb(63, 64, 71);
    --bg-toast: rgb(63, 64, 71);
    --bg-sidebar: rgb(46, 49, 66);
    --bg-sidebar-modern-layout: rgb(44, 45, 53);
    --bg-sidebar-item-selected: rgba(#{$white}, 0.25);
    --bg-shimmer-loader: linear-gradient(90deg, var(--N50) 0%, var(--N200) 50%, var(--N50) 80%);
    --bg-hover: rgba(229, 242, 255, 0.05);
    --bg-hover-opaque: rgb(41, 42, 51);
    --bg-hover-secondary-opaque: rgb(36, 38, 46);
    --bg-body: var(--bg-tertiary);
    --bg-segmented-control: rgba(#{$black}, 0.30);

    --backdrop-bg: rgba(#{$black}, 0.6);

    --border-primary: var(--N200);
    --border-primary-translucent: rgba(#{$white}, 0.2);
    --border-secondary: var(--N100);
    --border-secondary-translucent: rgba(#{$white}, 0.1);

    --divider-primary: var(--border-primary);
    --divider-primary-translucent: var(--border-primary-translucent);
    --divider-secondary: var(--border-secondary);
    --divider-secondary-translucent: var(--border-secondary-translucent);
}

:root {
    --white-10: rgba(#{$white}, 0.1);
    --white-20: rgba(#{$white}, 0.2);
    --white-40: rgba(#{$white}, 0.4);
    --white-60: rgba(#{$white}, 0.6);
    --white-80: rgba(#{$white}, 0.8);
    --white: rgb(#{$white});

    --black-20: rgba(#{$black}, 0.2);
    --black-40: rgba(#{$black}, 0.4);
    --black-60: rgba(#{$black}, 0.6);
    --black-80: rgba(#{$black}, 0.8);
    --black: rgb(0, 10, 20);

    // Being used as hex in LogViewer, Terminal for theming support
    --terminal-bg: rgb(24, 25, 32);
    --transparent: transparent;

    --button-text-on-dark: rgb(114, 172, 230);

    --shadow-menu: 0px 1px 1px 0px rgba(#{$black}, 0.07),
    0px 2px 5px 0px rgba(#{$black}, 0.07),
    0px 3px 8px 0px rgba(#{$black}, 0.07);
    --shadow-modal: 0px 1px 1px 0px rgba(#{$black}, 0.04),
    0px 2px 8px 0px rgba(#{$black}, 0.04),
    0px 3px 17px 0px rgba(#{$black}, 0.04),
    0px 4px 30px 0px rgba(#{$black}, 0.13);
    --shadow-overlay: 0px 1px 1px 0px rgba(#{$black}, 0.04),
    0px 2px 6px 0px rgba(#{$black}, 0.04),
    0px 4px 12px 0px rgba(#{$black}, 0.1);
    --shadow-drawer: 0px 1px 1px 0px rgba(#{$black}, 0.04),
    0px 2px 8px 0px rgba(#{$black}, 0.04),
    0px 3px 17px 0px rgba(#{$black}, 0.04),
    0px 4px 30px 0px rgba(#{$black}, 0.13);
    --shadow-10: 0px 0px 4px 0px var(--black-20);
    --shadow-20: 0px 0px 7px 0px var(--black-20);
    --shadow-30: 0px 0px 10px 0px var(--black-20);
    --shadow-key: 0px 2px 0px 0px rgba(7, 17, 26, 0.15);

    &.theme {
        &__light {
            @include theme-light;
        }

        &__dark {
            @include theme-dark;
        }
    }
}

.component-specific-theme {
    &__light {
        @include theme-light;

        $code-editor-diff-red: 243, 62, 62;
        $code-editor-diff-green: 29, 173, 112;
        $selection-color: 0, 102, 204;

        // CODE-EDITOR COLORS
        --fg-code-editor: rgb(26, 47, 101);
        --bg-code-editor-base: var(--bg-primary);
        --bg-code-editor-base-gutter: var(--bg-secondary);
        --bg-code-editor-red: rgba(#{$code-editor-diff-red}, 0.15);
        --bg-code-editor-red-gutter: rgba(#{$code-editor-diff-red}, 0.20);
        --bg-code-editor-red-highlight: rgba(#{$code-editor-diff-red}, 0.5);
        --bg-code-editor-green: rgba(#{$code-editor-diff-green}, 0.15);
        --bg-code-editor-green-gutter: rgba(#{$code-editor-diff-green}, 0.20);
        --bg-code-editor-green-highlight: rgba(#{$code-editor-diff-green}, 0.5);
        --bg-matching-keyword: rgb(197, 141, 54);
        --bg-matching-keyword-selected: rgb(0, 102, 204);
        --active-line: var(--bg-hover);
        --bg-code-editor-diffmap-scrollbar: rgba(#{$black}, 0.1);
        --selection-color: rgba(#{$selection-color}, 0.3);
        --selection-match-color: rgba(#{$selection-color}, 0.2);
        // CODE-EDITOR EDITOR COLORS
        --code-editor-property-name: rgb(0, 128, 128);
        --code-editor-number: rgb(0, 92, 197);
        --code-editor-boolean: rgb(107, 92, 204);
    }

    &__dark {
        @include theme-dark;

        $code-editor-diff-red: 255, 93, 96;
        $code-editor-diff-green: 122, 198, 128;
        $selection-color: 116, 181, 255;


        // CODE-EDITOR COLORS
        --fg-code-editor: #a5d6ff;
        --bg-code-editor-base: var(--terminal-bg);
        --bg-code-editor-base-gutter: var(--bg-tertiary);
        --bg-code-editor-red: rgba(#{$code-editor-diff-red}, 0.18);
        --bg-code-editor-red-gutter: rgba(#{$code-editor-diff-red}, 0.12);
        --bg-code-editor-red-highlight: rgba(#{$code-editor-diff-red}, 0.5);
        --bg-code-editor-green: rgba(#{$code-editor-diff-green}, 0.18);
        --bg-code-editor-green-gutter: rgba(#{$code-editor-diff-green}, 0.12);
        --bg-code-editor-green-highlight: rgba(#{$code-editor-diff-green}, 0.5);
        --bg-matching-keyword: rgb(197, 141, 54);
        --bg-matching-keyword-selected: rgb(0, 102, 204);
        --active-line: var(--bg-hover);
        --selection-color: rgba(#{$selection-color}, 0.4);
        --selection-match-color: rgba(#{$selection-color}, 0.3);
        --bg-code-editor-diffmap-scrollbar: rgba(#{$white}, 0.2);
        // CODE-EDITOR EDITOR COLORS
        --code-editor-property-name: rgb(126, 231, 135);
        --code-editor-number: rgb(121, 192, 255);
        --code-editor-boolean: rgb(192, 132, 252);
    }
}
