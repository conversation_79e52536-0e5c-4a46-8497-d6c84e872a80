/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React, { SyntheticEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { generatePath, Route, useHistory, useLocation, useParams, useRouteMatch } from 'react-router-dom'

import {
    ACTION_STATE,
    aggregateNodes,
    AppStatusModal,
    AppStatusModalTabType,
    ArtifactInfoModal,
    Button,
    DeploymentAppTypes,
    DeploymentStatusDetailsBreakdownDataType,
    DeploymentStatusDetailsType,
    DocLink,
    GenericEmptyState,
    getAppDetailsURL,
    getAppsInfoForEnv,
    getIsRequestAborted,
    handleAnalyticsEvent,
    Icon,
    MODAL_TYPE,
    noop,
    processDeploymentStatusDetailsData,
    Progressing,
    ReleaseMode,
    ServerErrors,
    showError,
    stringComparatorBySortOrder,
    ToastManager,
    ToastVariantType,
    useAsync,
    useMainContext,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as ForwardArrow } from '@Icons/ic-arrow-forward.svg'
import { ReactComponent as Trash } from '@Icons/ic-delete-dots.svg'
import AppNotConfiguredIcon from '@Images/app-not-configured.png'
import noGroups from '@Images/<EMAIL>'
import { URL_PARAM_MODE_TYPE } from '@Components/common/helpers/types'

import {
    DEFAULT_STATUS,
    DEPLOYMENT_STATUS,
    DEPLOYMENT_STATUS_QUERY_PARAM,
    RESOURCES_NOT_FOUND,
} from '../../../../config'
import { APP_DETAILS, ERROR_EMPTY_SCREEN } from '../../../../config/constantMessaging'
import { getAppConfigStatus, getAppOtherEnvironmentMin, stopStartApp } from '../../../../services/service'
import { useAppContext } from '../../../common'
import { AppDetailsEmptyState } from '../../../common/AppDetailsEmptyState'
import { ClusterMetaDataBar } from '../../../common/ClusterMetaDataBar/ClusterMetaDataBar'
import { importComponentFromFELibrary, sortOptionsByValue } from '../../../common/helpers/Helpers'
import { AppLevelExternalLinks } from '../../../externalLinks/ExternalLinks.component'
import { getExternalLinks } from '../../../externalLinks/ExternalLinks.service'
import { ExternalLinkIdentifierType, ExternalLinksAndToolsType } from '../../../externalLinks/ExternalLinks.type'
import { sortByUpdatedOn } from '../../../externalLinks/ExternalLinks.utils'
import { AppType, EnvType } from '../../../v2/appDetails/appDetails.type'
import IndexStore from '../../../v2/appDetails/index.store'
import { EmptyK8sResourceComponent } from '../../../v2/appDetails/k8Resource/K8Resource.component'
import NodeTreeDetailTab from '../../../v2/appDetails/NodeTreeDetailTab'
import RotatePodsModal from '../../../v2/appDetails/sourceInfo/rotatePods/RotatePodsModal.component'
import SyncErrorComponent from '../../../v2/appDetails/SyncError.component'
import { TriggerUrlModal } from '../../list/TriggerUrl'
import { fetchAppDetailsInTime, fetchResourceTreeInTime } from '../../service'
import { AggregatedNodes } from '../../types'
import { renderCIListHeader } from '../cdDetails/utils'
import { MATERIAL_TYPE } from '../triggerView/types'
import AppEnvSelector from './AppDetails.components'
import { getDeploymentStatusDetail } from './appDetails.service'
import {
    AppDetailProps,
    DeletedAppComponentType,
    DetailsType,
    ErrorItem,
    HibernationModalTypes,
} from './appDetails.type'
import AppDetailsCDModal from './AppDetailsCDModal'
import { AppMetrics } from './AppMetrics'
import { AG_APP_DETAILS_GA_EVENTS, DA_APP_DETAILS_GA_EVENTS } from './constants'
import HibernateModal from './HibernateModal'
import IssuesListingModal from './IssuesListingModal'
import { SourceInfo } from './SourceInfo'

const VirtualAppDetailsEmptyState = importComponentFromFELibrary('VirtualAppDetailsEmptyState')
const DeploymentWindowStatusModal = importComponentFromFELibrary('DeploymentWindowStatusModal')
const DeploymentWindowConfirmationDialog = importComponentFromFELibrary('DeploymentWindowConfirmationDialog')
const processVirtualEnvironmentDeploymentData = importComponentFromFELibrary(
    'processVirtualEnvironmentDeploymentData',
    null,
    'function',
)
let deploymentStatusTimer = null
let appDetailsIntervalID = null
const getDeploymentWindowProfileMetaData = importComponentFromFELibrary(
    'getDeploymentWindowProfileMetaData',
    null,
    'function',
)

const ConfigDriftModal = importComponentFromFELibrary('ConfigDriftModal', null, 'function')
const ExplainWithAIButton = importComponentFromFELibrary('ExplainWithAIButton', null, 'function')

export const AppNotConfigured = ({
    image,
    title,
    subtitle,
    buttonTitle,
    isJobView,
    renderCustomButton,
}: {
    image?: any
    title?: string
    subtitle?: React.ReactNode
    buttonTitle?: string
    isJobView?: boolean
    renderCustomButton?: () => JSX.Element
}) => {
    const { appId } = useParams<{ appId: string }>()
    const { push } = useHistory()

    const handleEditApp = () => {
        getAppConfigStatus(+appId, isJobView, false)
            .then(() => {
                const url = `/${isJobView ? 'job' : 'app'}/${appId}/edit`

                push(url)
            })
            .catch(noop)
    }

    const renderButton = () =>
        appId &&
        push && (
            <Button
                dataTestId="app-details-empty"
                text={buttonTitle || 'Go to app configurations'}
                onClick={handleEditApp}
                endIcon={<ForwardArrow />}
            />
        )

    return (
        <GenericEmptyState
            image={image || AppNotConfiguredIcon}
            title={title || 'Finish configuring this application'}
            subTitle={
                subtitle || (
                    <>
                        {APP_DETAILS.APP_FULLY_NOT_CONFIGURED}&nbsp;
                        <DocLink text={APP_DETAILS.NEED_HELP} docLinkKey="APP_CREATE" dataTestId="app-details-empty" />
                    </>
                )
            }
            isButtonAvailable
            renderButton={renderCustomButton ?? renderButton}
        />
    )
}

const DeletedAppComponent: React.FC<DeletedAppComponentType> = ({
    resourceTreeFetchTimeOut,
    showApplicationDetailedModal,
}) => {
    if (resourceTreeFetchTimeOut) {
        return (
            <>
                <div className="mt-16 mb-9">
                    <SyncErrorComponent showApplicationDetailedModal={showApplicationDetailedModal} />
                </div>
                <EmptyK8sResourceComponent emptyStateMessage={RESOURCES_NOT_FOUND} />
            </>
        )
    }
    return <AppDetailsEmptyState envType={EnvType.APPLICATION} />
}

const Details: React.FC<DetailsType> = ({
    appDetailsAPI,
    setAppDetailResultInParent,
    environment,
    environments,
    isPollingRequired = true,
    setIsAppDeleted,
    commitInfo,
    showCommitInfo,
    isAppDeleted,
    isVirtualEnvRef,
    isDeploymentBlocked,
    deploymentUserActionState,
    appDetails,
    setAppDetails,
    isAppView,
    applications,
}) => {
    const params = useParams<{ appId: string; envId: string }>()
    const location = useLocation()
    const { replace, push } = useHistory()
    const { path, url } = useRouteMatch()

    const { setAIAgentContext } = useMainContext()

    useEffect(() => {
        setAIAgentContext({
            path,
            context: {
                ...params,
                environmentName: appDetails?.environmentName ?? '',
                appName: appDetails?.appName ?? '',
            },
        })
    }, [appDetails?.environmentName, appDetails?.appName, url])

    const appDetailsFromIndexStore = IndexStore.getAppDetails()

    const [showAppStatusModal, setShowAppStatusModal] = useState<boolean>(false)
    const [resourceTreeFetchTimeOut, setResourceTreeFetchTimeOut] = useState<boolean>(false)
    const [urlInfo, setUrlInfo] = useState<boolean>(false)
    const [hibernateConfirmationModal, setHibernateConfirmationModal] = useState<HibernationModalTypes>(null)
    const [rotateModal, setRotateModal] = useState<boolean>(false)
    const [hibernating, setHibernating] = useState<boolean>(false)
    const [showIssuesModal, toggleIssuesModal] = useState<boolean>(false)
    const [CDModalMaterialType, setCDModalMaterialType] = useState<string | null>(null)
    const [appDetailsError, setAppDetailsError] = useState(undefined)

    const [hibernationPatchChartName, setHibernationPatchChartName] = useState<string>('')
    const [externalLinksAndTools, setExternalLinksAndTools] = useState<ExternalLinksAndToolsType>({
        externalLinks: [],
        monitoringTools: [],
    })

    const primaryResourceList = isAppView ? environments : applications

    // NOTE: this might seem like a duplicate of loadingResourceTree
    // but its not since loadingResourceTree runs a loader on the whole page
    const [isReloadResourceTreeInProgress, setIsReloadResourceTreeInProgress] = useState(false)

    const [loadingDetails, setLoadingDetails] = useState(true)
    const [loadingResourceTree, setLoadingResourceTree] = useState(true)
    const [errorsList, setErrorsList] = useState<ErrorItem[]>([])
    const appDetailsRef = useRef(null)
    const appDetailsRequestRef = useRef(null)
    const pollResourceTreeRef = useRef(true)
    const appDetailsAbortRef = useRef<AbortController>(null)

    const [deploymentStatusDetailsBreakdownData, setDeploymentStatusDetailsBreakdownData] =
        useState<DeploymentStatusDetailsBreakdownDataType>({
            ...(isVirtualEnvRef.current && processVirtualEnvironmentDeploymentData
                ? processVirtualEnvironmentDeploymentData()
                : processDeploymentStatusDetailsData()),
            deploymentStatus: DEFAULT_STATUS,
        })
    const isConfigDriftEnabled: boolean = window._env_.FEATURE_CONFIG_DRIFT_ENABLE && !!ConfigDriftModal
    const isExternalToolAvailable: boolean =
        externalLinksAndTools.externalLinks.length > 0 && externalLinksAndTools.monitoringTools.length > 0
    const interval = Number(window._env_.DEVTRON_APP_DETAILS_POLLING_INTERVAL) || 30000
    appDetailsRequestRef.current = appDetails?.deploymentAppDeleteRequest

    const aggregatedNodes: AggregatedNodes = useMemo(
        () => aggregateNodes(appDetails?.resourceTree?.nodes || [], appDetails?.resourceTree?.podMetadata || []),
        [appDetails],
    )

    const clearDeploymentStatusTimer = useCallback((): void => {
        if (deploymentStatusTimer) {
            clearTimeout(deploymentStatusTimer)
        }
    }, [deploymentStatusTimer])

    const processDeploymentStatusData = useCallback(
        (deploymentStatusDetailRes: DeploymentStatusDetailsType): void => {
            const processedDeploymentStatusDetailsData =
                isVirtualEnvRef.current && processVirtualEnvironmentDeploymentData
                    ? processVirtualEnvironmentDeploymentData(deploymentStatusDetailRes)
                    : processDeploymentStatusDetailsData(deploymentStatusDetailRes)

            clearDeploymentStatusTimer()

            if (processedDeploymentStatusDetailsData.deploymentStatus === DEPLOYMENT_STATUS.INPROGRESS) {
                deploymentStatusTimer = setTimeout(() => {
                    // !NOTE: cyclic dependency
                    // eslint-disable-next-line @typescript-eslint/no-use-before-define
                    getDeploymentDetailStepsData()
                }, 10000)
            }

            setDeploymentStatusDetailsBreakdownData(processedDeploymentStatusDetailsData)
        },
        [
            isVirtualEnvRef,
            processVirtualEnvironmentDeploymentData,
            processDeploymentStatusDetailsData,
            clearDeploymentStatusTimer,
            DEPLOYMENT_STATUS,
            setDeploymentStatusDetailsBreakdownData,
        ],
    )

    const getDeploymentDetailStepsData = useCallback((): void => {
        // Deployments status details for Devtron apps
        getDeploymentStatusDetail(params.appId, params.envId)
            .then((deploymentStatusDetailRes) => {
                processDeploymentStatusData(deploymentStatusDetailRes.result)
            })
            .catch(noop)
    }, [params.appId, params.envId, getDeploymentStatusDetail, processDeploymentStatusData])

    function clearPollingInterval() {
        if (appDetailsIntervalID) {
            clearInterval(appDetailsIntervalID)
            appDetailsIntervalID = null
        }
    }

    useEffect(
        () => () => {
            clearPollingInterval()
            clearDeploymentStatusTimer()
            IndexStore.clearAppDetails()
        },
        [],
    )

    useEffect(() => {
        appDetailsAbortRef.current = new AbortController()
        return () => {
            appDetailsAbortRef.current.abort()
        }
    }, [params.envId, params.appId])

    const handleAppDetailsCallError = (error) => {
        if (error.code === 404 || appDetailsRequestRef.current) {
            if (setIsAppDeleted) {
                setIsAppDeleted(true)
            }
            // NOTE: BE sends  string representation of 7000 instead of number 7000
            if (
                getIsRequestAborted(error) ||
                (error instanceof ServerErrors && String(error.errors?.[0]?.code ?? '') === '7000')
            ) {
                setResourceTreeFetchTimeOut(true)
            } else {
                setResourceTreeFetchTimeOut(false)
                setAppDetails(null)
            }
            clearPollingInterval()
        } else if (!appDetails) {
            setAppDetailsError(error)
        }
    }

    const fetchResourceTree = () => {
        if (appDetailsAbortRef.current) {
            appDetailsAbortRef.current.abort()
        }

        appDetailsAbortRef.current = new AbortController()

        setIsReloadResourceTreeInProgress(true)
        fetchResourceTreeInTime(params.appId, params.envId, interval - 5000, appDetailsAbortRef)
            .then((response) => {
                if (
                    response.errors &&
                    response.errors.length === 1 &&
                    response.errors[0].code === '7000' &&
                    appDetailsRequestRef.current
                ) {
                    if (setIsAppDeleted) {
                        setIsAppDeleted(true)
                    }
                    setResourceTreeFetchTimeOut(true)
                    clearPollingInterval()
                } else {
                    appDetailsRef.current = {
                        ...appDetailsRef.current,
                        resourceTree: response.result,
                    }
                    IndexStore.publishAppDetails(appDetailsRef.current, AppType.DEVTRON_APP)
                    setAppDetails(appDetailsRef.current)
                }
            })
            .catch(handleAppDetailsCallError)
            .finally(() => {
                setLoadingResourceTree(false)
                setIsReloadResourceTreeInProgress(false)
            })
    }

    function getExternalLinksAndTools(clusterId) {
        getExternalLinks(clusterId, params.appId, ExternalLinkIdentifierType.DevtronApp)
            .then((externalLinksRes) => {
                setExternalLinksAndTools({
                    externalLinks: externalLinksRes.result?.ExternalLinks?.sort(sortByUpdatedOn) || [],
                    monitoringTools:
                        externalLinksRes.result?.Tools?.map((tool) => ({
                            label: tool.name,
                            value: tool.id,
                            icon: tool.icon,
                        })).sort(sortOptionsByValue) || [],
                })
            })
            .catch(() => {
                setExternalLinksAndTools(externalLinksAndTools)
            })
    }

    function _getDeploymentStatusDetail(
        deploymentAppType: DeploymentAppTypes,
        isIsolatedEnv: boolean,
        triggerIdToFetch?: number,
    ) {
        // triggerIdToFetch represents the wfrId to fetch for any specific deployment
        getDeploymentStatusDetail(params.appId, params.envId, triggerIdToFetch?.toString())
            .then((deploymentStatusDetailRes) => {
                if (deploymentStatusDetailRes.result) {
                    // Timelines are not applicable for helm deployments and air gapped envs
                    if (deploymentAppType === DeploymentAppTypes.HELM || isIsolatedEnv) {
                        const processedDeploymentStatusData =
                            isVirtualEnvRef.current && processVirtualEnvironmentDeploymentData
                                ? processVirtualEnvironmentDeploymentData(deploymentStatusDetailRes.result)
                                : processDeploymentStatusDetailsData(deploymentStatusDetailRes.result)

                        setDeploymentStatusDetailsBreakdownData(processedDeploymentStatusData)
                    } else {
                        processDeploymentStatusData(deploymentStatusDetailRes.result)
                    }
                }
            })
            .catch(noop)
    }

    const callAppDetailsAPI = async (fetchExternalLinks?: boolean) => {
        try {
            const response = await appDetailsAPI(params.appId, params.envId, interval - 5000, appDetailsAbortRef)
            // eslint-disable-next-line no-param-reassign
            isVirtualEnvRef.current = response.result?.isVirtualEnvironment

            appDetailsRef.current = {
                ...appDetailsRef.current,
                ...response.result,
            }
            IndexStore.publishAppDetails(appDetailsRef.current, AppType.DEVTRON_APP)
            setAppDetails(appDetailsRef.current)

            // This means the CD is not triggered and the app is not helm migrated i.e. Empty State
            if (!response.result.isPipelineTriggered && response.result.releaseMode === ReleaseMode.NEW_DEPLOYMENT) {
                setResourceTreeFetchTimeOut(false)
                setLoadingResourceTree(false)
                pollResourceTreeRef.current = false
                return
            }

            if (fetchExternalLinks && response.result?.clusterId) {
                getExternalLinksAndTools(response.result.clusterId)
            }
            pollResourceTreeRef.current = true

            if (pollResourceTreeRef.current) {
                // Need to wait for the resource tree to check if the env is isolated or not
                await fetchResourceTree()
            }

            const isIsolatedEnv = isVirtualEnvRef.current && !!appDetailsRef.current.resourceTree

            _getDeploymentStatusDetail(
                appDetailsRef.current.deploymentAppType,
                isIsolatedEnv,
                isIsolatedEnv ? appDetailsRef.current?.resourceTree?.wfrId : null,
            )
        } catch (err) {
            handleAppDetailsCallError(err)
        } finally {
            setLoadingDetails(false)
        }
    }

    useEffect(() => {
        if (appDetails && setAppDetailResultInParent) {
            setAppDetailResultInParent(appDetails)
        }
    }, [appDetails])

    useEffect(() => {
        if (appDetailsError) {
            showError(appDetailsError)
        }
    }, [appDetailsError])

    useEffect(() => {
        clearPollingInterval()
        if (isPollingRequired && params.appId && params.envId) {
            appDetailsIntervalID = setInterval(callAppDetailsAPI, interval)
            callAppDetailsAPI(true).catch(noop)
        }
    }, [isPollingRequired, params.appId, params.envId])

    const handleHibernate = async () => {
        try {
            setHibernating(true)
            const isUnHibernateReq = ['hibernating', 'hibernated'].includes(
                appDetails.resourceTree.status.toLowerCase(),
            )
            await stopStartApp(Number(params.appId), Number(params.envId), isUnHibernateReq ? 'START' : 'STOP')
            await callAppDetailsAPI()
            ToastManager.showToast({
                variant: ToastVariantType.success,
                description: isUnHibernateReq ? 'Pods restore initiated' : 'Pods scale down initiated',
            })
        } catch (err) {
            showError(err)
        } finally {
            setHibernating(false)
            setHibernateConfirmationModal(null)
        }
    }

    const handleCloseAppStatusModal = (): void => {
        if (showAppStatusModal) {
            setShowAppStatusModal(false)
        }

        if (location.search.includes(DEPLOYMENT_STATUS_QUERY_PARAM)) {
            replace({
                search: '',
            })
        }
    }

    const showApplicationDetailedModal = (): void => {
        setShowAppStatusModal(true)
    }

    const handleOpenCDModal = (isForRollback?: boolean) => () => {
        push({
            search: new URLSearchParams({
                mode: URL_PARAM_MODE_TYPE.LIST,
            }).toString(),
        })
        setCDModalMaterialType(isForRollback ? MATERIAL_TYPE.rollbackMaterialList : MATERIAL_TYPE.inputMaterialList)

        if (isForRollback) {
            handleAnalyticsEvent(
                isAppView
                    ? DA_APP_DETAILS_GA_EVENTS.RollbackButtonClicked
                    : AG_APP_DETAILS_GA_EVENTS.RollbackButtonClicked,
            )
            return
        }
        handleAnalyticsEvent(
            isAppView ? DA_APP_DETAILS_GA_EVENTS.DeployButtonClicked : AG_APP_DETAILS_GA_EVENTS.DeployButtonClicked,
        )
    }

    const handleCloseCDModal = () => {
        setCDModalMaterialType(null)
        push({ search: '' })
    }

    const renderSelectImageButton = () => (
        <Button
            dataTestId="select-image-to-deploy"
            startIcon={<Icon name="ic-hand-pointing" color={null} />}
            text="Select Image to deploy"
            onClick={handleOpenCDModal()}
        />
    )

    const renderCDModal = () =>
        appDetails &&
        CDModalMaterialType && (
            <AppDetailsCDModal
                appId={appDetails.appId}
                environmentId={appDetails.environmentId}
                environmentName={appDetails.environmentName}
                isVirtualEnvironment={appDetails.isVirtualEnvironment}
                deploymentAppType={appDetails.deploymentAppType}
                loadingDetails={loadingDetails}
                cdModal={{
                    cdPipelineId: appDetails.cdPipelineId,
                    ciPipelineId: appDetails.ciPipelineId,
                    parentEnvironmentName: appDetails.parentEnvironmentName,
                    deploymentUserActionState,
                    triggerType: appDetails.triggerType,
                }}
                handleSuccess={callAppDetailsAPI}
                materialType={CDModalMaterialType}
                closeCDModal={handleCloseCDModal}
            />
        )

    if (
        !loadingResourceTree &&
        (!appDetails?.resourceTree || !appDetails.resourceTree.nodes?.length) &&
        (!appDetails?.isPipelineTriggered || isAppDeleted)
    ) {
        return (
            <>
                {!!primaryResourceList.length && (
                    <div className="flex left ml-20 mt-16">
                        <AppEnvSelector
                            {...(isAppView ? { isAppView, environments } : { isAppView: false, applications })}
                        />
                        {isAppDeleted && appDetails?.deploymentAppDeleteRequest && (
                            <div data-testid="deleteing-argocd-pipeline" className="flex left">
                                <Trash className="icon-dim-16 mr-8 ml-12" />
                                <span className="cr-5 fw-6">Deleting deployment pipeline </span>
                                <span className="dc__loading-dots cr-5" />
                            </div>
                        )}
                    </div>
                )}
                {isAppDeleted ? (
                    <DeletedAppComponent
                        resourceTreeFetchTimeOut={resourceTreeFetchTimeOut}
                        showApplicationDetailedModal={showApplicationDetailedModal}
                    />
                ) : (
                    <>
                        <AppNotConfigured
                            image={noGroups}
                            title={ERROR_EMPTY_SCREEN.ALL_SET_GO_CONFIGURE}
                            subtitle={ERROR_EMPTY_SCREEN.DEPLOYEMENT_WILL_BE_HERE}
                            renderCustomButton={renderSelectImageButton}
                        />
                        {renderCDModal()}
                    </>
                )}
            </>
        )
    }

    const environmentName = environments.find((env) => env.environmentId === +params.envId)?.environmentName

    const renderAppDetails = (): JSX.Element => {
        if (!appDetails.resourceTree && isVirtualEnvRef.current && VirtualAppDetailsEmptyState) {
            return <VirtualAppDetailsEmptyState environmentName={environmentName} />
        }
        return (
            <NodeTreeDetailTab
                appDetails={appDetails}
                externalLinks={externalLinksAndTools.externalLinks}
                monitoringTools={externalLinksAndTools.monitoringTools}
                isDevtronApp
                isDeploymentBlocked={isDeploymentBlocked}
                isVirtualEnvironment={isVirtualEnvRef.current}
                handleReloadResourceTree={fetchResourceTree}
                isReloadResourceTreeInProgress={isReloadResourceTreeInProgress}
            />
        )
    }

    const handleHibernateConfirmationModalClose = (e?: SyntheticEvent) => {
        e?.stopPropagation()
        setHibernateConfirmationModal(null)
    }

    const renderHibernateModal = (): JSX.Element => {
        if (hibernateConfirmationModal && isDeploymentBlocked && DeploymentWindowConfirmationDialog) {
            return (
                <DeploymentWindowConfirmationDialog
                    onClose={handleHibernateConfirmationModalClose}
                    isLoading={hibernating}
                    type={hibernateConfirmationModal === 'hibernate' ? MODAL_TYPE.HIBERNATE : MODAL_TYPE.UNHIBERNATE}
                    onClickActionButton={handleHibernate}
                    appName={appDetails.appName}
                    envName={appDetails.environmentName}
                    appId={params.appId}
                    envId={params.envId}
                />
            )
        }

        return (
            <HibernateModal
                appName={appDetails.appName}
                envName={appDetails.environmentName}
                hibernating={hibernating}
                handleHibernate={handleHibernate}
                chartName={hibernationPatchChartName}
                hibernateConfirmationModal={hibernateConfirmationModal}
                handleHibernateConfirmationModalClose={handleHibernateConfirmationModalClose}
            />
        )
    }

    const onClickRotatePodClose = () => setRotateModal(false)

    const renderRestartWorkload = () => (
        <RotatePodsModal
            onClose={onClickRotatePodClose}
            callAppDetailsAPI={callAppDetailsAPI}
            isDeploymentBlocked={isDeploymentBlocked}
        />
    )

    const updateDeploymentStatusDetailsBreakdownData = (updatedTimelines: DeploymentStatusDetailsBreakdownDataType) => {
        setDeploymentStatusDetailsBreakdownData(updatedTimelines)
    }

    const isDeploymentAppDeleting = appDetails?.deploymentAppDeleteRequest || false
    return (
        <>
            <div
                className={`w-100 pt-16 pr-20 pb-16 pl-20 dc__gap-16 ${isDeploymentAppDeleting ? 'app-info-bg' : 'app-info-bg-gradient'}`}
            >
                <SourceInfo
                    appDetails={appDetails}
                    setDetailed={setShowAppStatusModal}
                    environment={environment}
                    isAppView={isAppView}
                    environments={environments}
                    showCommitInfo={showCommitInfo}
                    showUrlInfo={setUrlInfo}
                    showHibernateModal={setHibernateConfirmationModal}
                    deploymentStatusDetailsBreakdownData={deploymentStatusDetailsBreakdownData}
                    isVirtualEnvironment={isVirtualEnvRef.current}
                    setRotateModal={setRotateModal}
                    loadingDetails={loadingDetails}
                    loadingResourceTree={loadingResourceTree}
                    toggleIssuesModal={toggleIssuesModal}
                    envId={appDetails?.environmentId}
                    ciArtifactId={appDetails?.ciArtifactId}
                    setErrorsList={setErrorsList}
                    deploymentUserActionState={deploymentUserActionState}
                    setHibernationPatchChartName={setHibernationPatchChartName}
                    applications={applications}
                    isResourceTreeReloading={isReloadResourceTreeInProgress}
                    handleOpenCDModal={handleOpenCDModal}
                />
            </div>
            {!loadingDetails && !loadingResourceTree && !appDetails?.deploymentAppDeleteRequest && (
                <>
                    {environment && !isVirtualEnvRef.current && (
                        <AppMetrics
                            appName={appDetails.appName}
                            addExtraSpace={!isExternalToolAvailable}
                            environment={environment}
                            podMap={aggregatedNodes.nodes.Pod}
                            k8sVersion={appDetails.k8sVersion}
                        />
                    )}
                    {isExternalToolAvailable && (
                        <AppLevelExternalLinks
                            appDetails={appDetails}
                            externalLinks={externalLinksAndTools.externalLinks}
                            monitoringTools={externalLinksAndTools.monitoringTools}
                        />
                    )}
                </>
            )}
            {loadingResourceTree ? (
                <div className="bg__primary h-100">
                    <Progressing pageLoader fullHeight size={32} fillColor="var(--N500)" />
                </div>
            ) : (
                renderAppDetails()
            )}
            {(showAppStatusModal || (appDetails && location.search.includes(DEPLOYMENT_STATUS_QUERY_PARAM))) && (
                <AppStatusModal
                    titleSegments={[appDetailsFromIndexStore.appName, appDetailsFromIndexStore.environmentName]}
                    handleClose={handleCloseAppStatusModal}
                    type="devtron-app"
                    appDetails={appDetailsFromIndexStore}
                    isConfigDriftEnabled={isConfigDriftEnabled}
                    configDriftModal={ConfigDriftModal}
                    initialTab={
                        showAppStatusModal ? AppStatusModalTabType.APP_STATUS : AppStatusModalTabType.DEPLOYMENT_STATUS
                    }
                    processVirtualEnvironmentDeploymentData={processVirtualEnvironmentDeploymentData}
                    updateDeploymentStatusDetailsBreakdownData={updateDeploymentStatusDetailsBreakdownData}
                    debugWithAIButton={ExplainWithAIButton}
                />
            )}
            {location.search.includes('deployment-window-status') && DeploymentWindowStatusModal && (
                <DeploymentWindowStatusModal envId={params.envId} appId={params.appId} />
            )}
            {showIssuesModal && (
                <IssuesListingModal errorsList={errorsList} closeIssuesListingModal={() => toggleIssuesModal(false)} />
            )}
            {urlInfo && (
                <TriggerUrlModal
                    appId={params.appId}
                    envId={params.envId}
                    appType={appDetails.appType}
                    close={() => setUrlInfo(false)}
                />
            )}
            {commitInfo && (
                <ArtifactInfoModal
                    envId={appDetails?.environmentId}
                    ciArtifactId={appDetails?.ciArtifactId}
                    handleClose={() => showCommitInfo(false)}
                    renderCIListHeader={renderCIListHeader}
                />
            )}
            {appDetails && !!hibernateConfirmationModal && renderHibernateModal()}
            {rotateModal && renderRestartWorkload()}
            {renderCDModal()}
        </>
    )
}

const AppDetail = ({ detailsType, filteredResourceIds }: AppDetailProps) => {
    const params = useParams<{ appId: string; envId: string }>()
    const { replace } = useHistory()
    const { path } = useRouteMatch()
    const { environmentId, setEnvironmentId } = useAppContext() // global state for app to synchronise environments
    const [isAppDeleted, setIsAppDeleted] = useState(false)

    const isAppView = detailsType === 'app'

    const [otherEnvsLoading, otherEnvsResult] = useAsync(
        () => getAppOtherEnvironmentMin(params.appId, false),
        [params.appId],
        !!params.appId,
    )

    const [, otherAppsResult] = useAsync(
        () => getAppsInfoForEnv({ envId: +params.envId }),
        [params.envId],
        !!params.envId && !isAppView,
    )

    const [commitInfo, showCommitInfo] = useState<boolean>(false)
    const [deploymentUserActionState, setDeploymentUserActionState] = useState<ACTION_STATE>(ACTION_STATE.ALLOWED)
    const isVirtualEnvRef = useRef(false)
    const [showDeploymentWindowConfirmation, setShowDeploymentWindowConfirmation] = useState(false)
    const [appDetails, setAppDetails] = useState(undefined)

    const filteredEntityMap = filteredResourceIds?.split(',').reduce((agg, curr) => agg.set(+curr, true), new Map())

    const envList = useMemo(
        () =>
            (otherEnvsResult?.result ?? [])
                .filter((env) => !isAppView || !filteredEntityMap || filteredEntityMap.get(env.environmentId))
                .sort((a, b) => stringComparatorBySortOrder(a.environmentName, b.environmentName)),
        [filteredResourceIds, otherEnvsResult],
    )

    const appList = useMemo(
        () =>
            (otherAppsResult?.apps ?? [])
                .filter((app) => !filteredEntityMap || filteredEntityMap.get(app.appId))
                .sort((a, b) => stringComparatorBySortOrder(a.appName, b.appName)),
        [filteredResourceIds, otherAppsResult],
    )

    useEffect(() => {
        if (isAppView) {
            const userDefinedEnvId = +params.envId || environmentId
            const selectedEnvId =
                userDefinedEnvId && envList.some((env) => env.environmentId === userDefinedEnvId)
                    ? userDefinedEnvId
                    : envList[0]?.environmentId

            if (envList.length && selectedEnvId && selectedEnvId !== +params.envId) {
                const newUrl = getAppDetailsURL(params.appId, selectedEnvId)
                replace(newUrl)
                return
            }
            return
        }

        const selectedAppId =
            +params.appId && appList.some((app) => app.appId === +params.appId) ? +params.appId : appList[0]?.appId

        if (appList.length && selectedAppId !== +params.appId) {
            const newUrl = generatePath(path, { appId: selectedAppId, envId: params.envId })
            replace(newUrl)
        }
    }, [filteredResourceIds, otherEnvsResult, otherAppsResult])

    useEffect(() => {
        if (!params.envId || !params.appId) {
            return
        }
        // Setting environmentId in app context only in case of app details and not env details
        if (isAppView) {
            setEnvironmentId(Number(params.envId))
        }
        setIsAppDeleted(false)
        if (getDeploymentWindowProfileMetaData) {
            getDeploymentWindowProfileMetaData(params.appId, params.envId).then(({ userActionState }) => {
                setDeploymentUserActionState(userActionState)
                if (userActionState && userActionState !== ACTION_STATE.ALLOWED) {
                    setShowDeploymentWindowConfirmation(true)
                } else {
                    setShowDeploymentWindowConfirmation(false)
                }
            })
        }
    }, [params.appId, params.envId])

    const renderAppNotConfigured = () => (
        <>
            {envList.length === 0 && !isAppDeleted && <AppNotConfigured />}
            {!params.envId && envList.length > 0 && (
                <GenericEmptyState
                    image={AppNotConfiguredIcon}
                    title="Please select an environment to view app details"
                />
            )}
        </>
    )

    const environment = useMemo(
        () => envList.find((env) => env.environmentId === +params.envId),
        [envList, params.envId],
    )

    return (
        <>
            <div className="dc__overflow-hidden flex-grow-1 flexbox-col dc__position-rel">
                <div
                    data-testid="app-details-wrapper"
                    className="app-details-page-wrapper flex-grow-1 dc__overflow-auto mw-none"
                >
                    {!params.envId && envList.length > 0 && (
                        <div className="w-100 pt-16 pr-20 pb-20 pl-20">
                            <SourceInfo
                                appDetails={null}
                                environments={envList}
                                environment={environment}
                                isAppView={isAppView}
                            />
                        </div>
                    )}
                    {!params.envId && otherEnvsLoading && <Progressing pageLoader />}
                    <Route path={`${path.replace(':envId(\\d+)?', ':envId(\\d+)')}`}>
                        <Details
                            key={`${params.appId}-${params.envId}`}
                            appDetailsAPI={fetchAppDetailsInTime}
                            isAppDeployment
                            environment={environment}
                            environments={envList}
                            setIsAppDeleted={setIsAppDeleted}
                            commitInfo={commitInfo}
                            showCommitInfo={showCommitInfo}
                            isAppDeleted={isAppDeleted}
                            isVirtualEnvRef={isVirtualEnvRef}
                            isDeploymentBlocked={showDeploymentWindowConfirmation}
                            deploymentUserActionState={deploymentUserActionState}
                            appDetails={appDetails}
                            setAppDetails={setAppDetails}
                            applications={appList}
                            isAppView={isAppView}
                        />
                    </Route>
                    {otherEnvsResult && !otherEnvsLoading && !isVirtualEnvRef.current && renderAppNotConfigured()}
                </div>
            </div>
            <ClusterMetaDataBar
                clusterName={appDetails?.clusterName}
                namespace={appDetails?.namespace}
                clusterId={appDetails?.clusterId}
                isVirtualEnvironment={isVirtualEnvRef.current}
            />
        </>
    )
}

export default AppDetail
