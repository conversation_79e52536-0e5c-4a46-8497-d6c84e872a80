import {
    AboutDevtronBody,
    Button,
    ComponentSizeType,
    DevtronProgressing,
    GenericSectionErrorState,
    LicenseInfoDialogType,
    SegmentedControl,
} from '@devtron-labs/devtron-fe-common-lib'

import { ABOUT_DEVTRON_TABS } from './constants'
import { LicenseInfo } from './LicenseInfoDialog.components'
import { AboutDevtronLicenseInfoProps } from './types'

const AboutDevtronLicenseInfo = ({
    dialogType,
    licenseData,
    handleChangeDialogType,
    handleUpdateLicenseClick,
    handleCloseLicenseInfoDialog,
    isLoading,
    licenseDataError,
    reloadLicenseData,
}: AboutDevtronLicenseInfoProps) => {
    const renderLicenseInfoBody = () => {
        if (isLoading) {
            return <DevtronProgressing parentClasses="flex h-200" classes="icon-dim-48" />
        }

        if (licenseDataError) {
            return <GenericSectionErrorState rootClassName="h-200" reload={reloadLicenseData} />
        }

        return <LicenseInfo licenseData={licenseData} handleUpdateLicenseClick={handleUpdateLicenseClick} />
    }

    return (
        <>
            {/* Header */}
            <div className="px-24 pt-24 pb-8">
                <SegmentedControl
                    name="about-devtron-segmented-control"
                    segments={ABOUT_DEVTRON_TABS}
                    value={dialogType}
                    onChange={handleChangeDialogType}
                    fullWidth
                />
            </div>
            {/* Body */}
            <div className="p-24 mxh-500 dc__overflow-auto">
                {dialogType === LicenseInfoDialogType.ABOUT ? <AboutDevtronBody /> : renderLicenseInfoBody()}
            </div>
            {/* Footer */}
            <div className="flex px-24 py-20 dc__content-end">
                <Button
                    dataTestId="license-info-okay"
                    text="Okay"
                    size={ComponentSizeType.medium}
                    onClick={handleCloseLicenseInfoDialog}
                />
            </div>
        </>
    )
}

export default AboutDevtronLicenseInfo
