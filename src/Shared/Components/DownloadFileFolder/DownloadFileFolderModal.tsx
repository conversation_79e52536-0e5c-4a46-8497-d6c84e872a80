/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useState } from 'react'
import { useParams } from 'react-router-dom'

import {
    AppType,
    Button,
    ButtonStyleType,
    ButtonVariantType,
    CustomInput,
    getUrlWithSearchParams,
    stopPropagation,
    useDownload,
    useSearchString,
    VisibleModal2,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as ICDownload } from '../../../Assets/Icons/ic-arrow-line-down.svg'
import { ReactComponent as ICClose } from '../../../Assets/Icons/ic-close.svg'
import { ROUTES } from '../../../Common/Constants'
import { DownloadContentParamsType, DownloadFileFolderModalParams, DownloadFileFolderModalProps } from './types'
import { getAppIdentifierAndType, getDisabledStateMessage } from './utils'

const DownloadFileFolderModal = ({
    handleClose,
    appDetails,
    isResourceBrowserView,
    isClusterTerminalView,
    containerName,
    clusterViewPodName,
}: DownloadFileFolderModalProps) => {
    const {
        appId,
        envId,
        clusterId,
        namespace,
        podName,
        name: resourceName,
    } = useParams<DownloadFileFolderModalParams>()
    const clusterTerminalViewParams = useSearchString()
    const [filePath, setFilePath] = useState<string>('')
    const [errorMessage, setErrorMessage] = useState('')
    const { handleDownload, isDownloading } = useDownload()

    const generateDownloadUrl = (): string => {
        if (isResourceBrowserView) {
            const baseUrl = `${ROUTES.K8S_DOWNLOAD_POD_CONTENT}/${clusterId}/${namespace}/${resourceName}/${containerName}`
            const params: DownloadContentParamsType = {
                path: filePath,
            }
            return getUrlWithSearchParams(baseUrl, params)
        }
        if (isClusterTerminalView) {
            const { namespace: clusterViewNamespace } = clusterTerminalViewParams.searchParams

            const baseUrl = `${ROUTES.K8S_DOWNLOAD_POD_CONTENT}/${clusterId}/${clusterViewNamespace}/${clusterViewPodName}/${containerName}`
            const params: DownloadContentParamsType = {
                path: filePath,
            }
            return getUrlWithSearchParams(baseUrl, params)
        }
        const { appIdentifier, appType } = getAppIdentifierAndType({ appDetails, appId, envId })
        // Use node namespace instead of app namespace as they both might differ in case of external apps
        const selectedNamespace = appDetails.resourceTree?.nodes?.find((nd) => nd.name === podName)?.namespace
        const isExternalArgoApp = appDetails.appType === AppType.EXTERNAL_ARGO_APP
        const baseUrl = `${ROUTES.K8S_DOWNLOAD_POD_CONTENT}/${appIdentifier}/${selectedNamespace}/${podName}/${containerName}`
        const params: DownloadContentParamsType = {
            path: filePath,
            appType,
            ...(isExternalArgoApp && { externalArgoApplicationName: appDetails.appName }),
        }
        return getUrlWithSearchParams(baseUrl, params)
    }

    const validateFilePath = (updatedFilePath: string): boolean => {
        if (!updatedFilePath) {
            setErrorMessage('You must provide a valid file path.')
            return false
        }
        if (updatedFilePath[0] !== '/') {
            setErrorMessage('File path must start with /')
            return false
        }
        setErrorMessage('')
        return true // Path is valid
    }

    const handleFilePathChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
        const updatedFilePath = event.target.value
        setFilePath(updatedFilePath)
        validateFilePath(updatedFilePath)
    }

    const disabledMessage = getDisabledStateMessage(filePath, errorMessage)

    const handleDownloadFile = async () => {
        if (disabledMessage) {
            return
        }
        const downloadUrl = generateDownloadUrl()
        await handleDownload({ downloadUrl, showFilePreparingToast: true })
        handleClose()
    }

    return (
        <VisibleModal2 className="visible-modal__body" close={handleClose}>
            <div
                className="flexbox-col bg__primary w-500 dc__m-auto mt-40 dc__border-radius-8-imp dc__overflow-hidden"
                onClick={stopPropagation}
            >
                <div className="flexbox dc__align-items-center dc__content-space py-12 px-20 dc__border-bottom">
                    <div className="flexbox dc__align-items-center dc__gap-12">
                        <ICDownload className="icon-dim-20 dc__no-shrink" />
                        <span className="fs-16 fw-6 lh-24 dc__content-space">Download file/folder</span>
                    </div>
                    <button
                        type="button"
                        className="dc__transparent p-0-imp h-20"
                        onClick={handleClose}
                        aria-label="close-button"
                    >
                        <ICClose className="icon-dim-20 dc__no-shrink" />
                    </button>
                </div>
                <div className="px-20 py-16 h-200">
                    <CustomInput
                        name="file-path"
                        value={filePath}
                        onChange={handleFilePathChange}
                        label="File Path"
                        required
                        error={errorMessage}
                        placeholder="/home/<USER>/filename.txt"
                        disabled={isDownloading}
                        helperText="Please provide absolute file path"
                    />
                </div>
                <div className="px-20 py-16 flexbox dc__align-item-center flex-justify-end dc__gap-12">
                    <Button
                        dataTestId="cancel-download"
                        text="Close"
                        variant={ButtonVariantType.secondary}
                        style={ButtonStyleType.neutral}
                        onClick={handleClose}
                    />
                    <div>
                        <Button
                            dataTestId="download-content"
                            text="Download"
                            isLoading={isDownloading}
                            disabled={!!disabledMessage}
                            onClick={handleDownloadFile}
                            showTooltip={!!disabledMessage}
                            tooltipProps={{
                                content: disabledMessage,
                            }}
                        />
                    </div>
                </div>
            </div>
        </VisibleModal2>
    )
}

export default DownloadFileFolderModal
