/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useState } from 'react'
import ReactGA from 'react-ga4'

import { Button, ButtonVariantType, ComponentSizeType, Icon } from '@devtron-labs/devtron-fe-common-lib'

import DownloadFileFolderModal from './DownloadFileFolderModal'
import { DownloadFileFolderButtonProps } from './types'

const DownloadFileFolderButton = (props: DownloadFileFolderButtonProps) => {
    const [showDownloadFileFolderModal, setShowDownloadFileFolderModal] = useState<boolean>(false)
    const handleShowDownloadModal = () => {
        setShowDownloadFileFolderModal(true)
        ReactGA.event({
            category: 'Terminal',
            action: 'Download File',
        })
    }

    const handleClose = () => {
        setShowDownloadFileFolderModal(false)
    }

    return (
        <>
            <span className="dc__border-right h-16" />
            <Button
                dataTestId="download-file-folder"
                onClick={handleShowDownloadModal}
                text="Download file/folder"
                variant={ButtonVariantType.text}
                size={ComponentSizeType.small}
                startIcon={<Icon name="ic-arrow-right" color={null} rotateBy={90} />}
            />
            {showDownloadFileFolderModal && <DownloadFileFolderModal {...props} handleClose={handleClose} />}
        </>
    )
}

export default DownloadFileFolderButton
