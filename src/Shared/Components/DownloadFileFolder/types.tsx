/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { AppDetails } from '@devtron-labs/devtron-fe-common-lib'

export type DownloadFileFolderModalParams =
    | {
          // Params available in app details view
          appId: string
          envId: string
          podName: string
          // Params available in resource browser view
          clusterId?: never
          name?: never
          namespace?: never
      }
    | {
          // Params available in app details view
          appId?: never
          envId?: never
          podName?: never
          // Params available in resource browser view
          clusterId: string
          name: string
          namespace: string
      }

export interface AppIdentifierAppType {
    appIdentifier: string
    appType: number
}

export interface DownloadFileFolderButtonProps {
    appDetails?: AppDetails
    isResourceBrowserView: boolean
    isClusterTerminalView: boolean
    containerName: string
    clusterViewPodName?: string
}

export interface DownloadFileFolderModalProps extends DownloadFileFolderButtonProps {
    handleClose: () => void
}

export interface AppIdentifierAndTypeUtilProps
    extends Required<Pick<DownloadFileFolderButtonProps, 'appDetails'>>,
        Required<Pick<DownloadFileFolderModalParams, 'appId' | 'envId'>> {}

export interface DownloadContentParamsType {
    path: string
    appType?: number
    externalArgoApplicationName?: string
}
