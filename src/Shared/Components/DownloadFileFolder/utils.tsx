/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { AppType, K8sResourcePayloadAppType } from '@devtron-labs/devtron-fe-common-lib'

import { AppIdentifierAndTypeUtilProps, AppIdentifierAppType } from './types'

export const getAppIdentifierAndType = ({
    appDetails,
    appId,
    envId,
}: AppIdentifierAndTypeUtilProps): AppIdentifierAppType => {
    if (appDetails.appType === AppType.DEVTRON_APP) {
        return {
            appIdentifier: `${appDetails.clusterId}|${appId}|${envId}`,
            appType: K8sResourcePayloadAppType.DEVTRON_APP,
        }
    }
    if (appDetails.appType === AppType.EXTERNAL_ARGO_APP) {
        return { appIdentifier: `${appDetails.clusterId}`, appType: K8sResourcePayloadAppType.EXTERNAL_ARGO_APP }
    }
    if (appDetails.appType === AppType.EXTERNAL_FLUX_APP) {
        return {
            appIdentifier: `${appDetails.clusterId}|${appDetails.namespace}|${appDetails.appName}|${appDetails.fluxTemplateType === 'Kustomization'}`,
            appType: K8sResourcePayloadAppType.EXTERNAL_FLUX_APP,
        }
    }
    return {
        appIdentifier: `${appDetails.clusterId}|${appDetails.namespace}|${appDetails.appName}`,
        appType: K8sResourcePayloadAppType.HELM_APP,
    }
}

export const getDisabledStateMessage = (filePath: string, errorMessage: string): string | null => {
    if (!filePath) {
        return 'Please enter a file path'
    }
    if (errorMessage) {
        return errorMessage
    }
    return null
}
