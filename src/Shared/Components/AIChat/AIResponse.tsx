import { useEffect, useState } from 'react'

import { ButtonVariantType, Icon, InfoBlock, InfoBlockVariant, MarkDown } from '@devtron-labs/devtron-fe-common-lib'

import { ThoughtBubble } from './ThoughtBubble'
import { AIResponseProps } from './types'

export const AIResponse = ({ aiResponse, onRetryClick, updateLastAnimationTime }: AIResponseProps) => {
    const { content, error, finalOutput, isAIResponseLoading } = aiResponse ?? {}
    const wasPromptAborted = !!error && error.message?.search('abort|aborted') > -1

    const [expandThoughtBubble, setExpandThoughtBubble] = useState(!finalOutput)

    useEffect(() => {
        if (finalOutput) {
            setExpandThoughtBubble(false)
        }
    }, [finalOutput])

    return (
        <div className="flexbox-col dc__gap-16 dc__align-start w-100">
            {(isAIResponseLoading || content) && !wasPromptAborted && (
                <ThoughtBubble
                    showThoughBubble={expandThoughtBubble}
                    setShowThoughtBubble={setExpandThoughtBubble}
                    isThinking={isAIResponseLoading}
                    updateLastAnimationTime={updateLastAnimationTime}
                    error={error}
                    onRetryClick={onRetryClick}
                >
                    {content && (
                        <MarkDown
                            markdown={content}
                            className="fs-13 fw-4 lh-20 cn-7 m-0 dc__no-border dc__word-break dc__text-wrap dc__open-sans p-0-imp dc__overflow-hidden w-100"
                        />
                    )}
                </ThoughtBubble>
            )}

            {finalOutput && (
                <MarkDown
                    markdown={finalOutput}
                    className="fs-13 fw-4 lh-20 cn-9 m-0 dc__no-border dc__word-break dc__text-wrap dc__open-sans p-0-imp dc__overflow-hidden w-100"
                />
            )}

            {wasPromptAborted && (
                <div className="pr-20 w-100">
                    <InfoBlock
                        heading="Prompt was aborted"
                        variant={InfoBlockVariant.NEUTRAL}
                        layout="column"
                        buttonProps={{
                            dataTestId: 'ai-chat__retry',
                            onClick: onRetryClick,
                            text: 'Retry',
                            variant: ButtonVariantType.text,
                            startIcon: <Icon name="ic-arrow-clockwise" color={null} />,
                        }}
                    />
                </div>
            )}

            {error && !wasPromptAborted && !content && (
                <div className="pr-20 w-100">
                    <InfoBlock
                        heading="Failed to respond"
                        description="Something seems to have gone wrong"
                        variant={InfoBlockVariant.ERROR}
                        layout="column"
                        buttonProps={{
                            dataTestId: 'ai-chat__retry',
                            onClick: onRetryClick,
                            text: 'Retry',
                            variant: ButtonVariantType.text,
                            startIcon: <Icon name="ic-arrow-clockwise" color={null} />,
                        }}
                    />
                </div>
            )}
        </div>
    )
}
