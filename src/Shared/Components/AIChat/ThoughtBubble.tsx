import { PropsWithChildren } from 'react'

import {
    AnimatePresence,
    BlinkingCursor,
    ButtonVariantType,
    Icon,
    InfoBlock,
    InfoBlockVariant,
    motion,
} from '@devtron-labs/devtron-fe-common-lib'

import { ThoughtBubbleProps } from './types'

export const ThoughtBubble = ({
    showThoughBubble: expand,
    setShowThoughtBubble: setExpand,
    updateLastAnimationTime,
    isThinking,
    children,
    error,
    onRetryClick,
}: PropsWithChildren<ThoughtBubbleProps>) => {
    const onClick = () => {
        if (!isThinking) {
            setExpand((prev) => !prev)
        }
    }

    return (
        <div
            className="flexbox"
            // NOTE: using width calc because markdown pre blocks require it for scroll to work
            style={{ width: 'calc(100% - 20px)' }}
        >
            <div
                className={`flexbox-col dc__gap-8 border__primary-translucent br-12 dc__overflow-hidden ${error ? 'bcr-1' : ''}`}
            >
                <div className="flexbox-col dc__gap-8 px-12 py-8 bg__primary br-12">
                    <div
                        role="button"
                        tabIndex={0}
                        onClick={onClick}
                        className={`flexbox dc__gap-4 dc__align-items-center dc__align-self-start bg__primary ${isThinking ? 'cursor-default' : ''}`}
                    >
                        <div className="flexbox dc__gap-8">
                            <Icon name="ic-brain" color="N900" size={20} />

                            <span className="fs-13 fw-6 cn-9 lh-1-5">{isThinking ? 'Thinking' : 'Thoughts'}</span>

                            {isThinking && !children && <BlinkingCursor />}
                        </div>

                        {!isThinking && (
                            <Icon name="ic-caret-down-small" color="N700" size={16} rotateBy={expand ? 180 : 0} />
                        )}
                    </div>

                    <AnimatePresence onExitComplete={updateLastAnimationTime} initial={false}>
                        {expand && children && (
                            <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{
                                    height: 'auto',
                                    opacity: 1,
                                }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3, ease: 'easeIn' }}
                                className="bg__primary dc__overflow-hidden"
                                onAnimationComplete={updateLastAnimationTime}
                            >
                                {children}
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>

                {error && (
                    <InfoBlock
                        heading="Failed to respond"
                        description="Something seems to have gone wrong"
                        variant={InfoBlockVariant.ERROR}
                        layout="column"
                        borderConfig={{ top: false, bottom: false, left: false, right: false }}
                        buttonProps={{
                            dataTestId: 'ai-chat__retry',
                            onClick: onRetryClick,
                            text: 'Retry',
                            variant: ButtonVariantType.text,
                            startIcon: <Icon name="ic-arrow-clockwise" color={null} />,
                        }}
                    />
                )}
            </div>
        </div>
    )
}
