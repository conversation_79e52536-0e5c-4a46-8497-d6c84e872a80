import { Fragment, RefCallback, useMemo, useRef, useState } from 'react'

import {
    Button,
    ButtonStyleType,
    ButtonVariantType,
    ComponentSizeType,
    GenericEmptyState,
    Icon,
    motion,
    noop,
    SegmentedControl,
    SegmentedControlProps,
    Textarea,
    useAnimationControls,
    useMainContext,
    useUserEmail,
} from '@devtron-labs/devtron-fe-common-lib'

import { AIResponse } from './AIResponse'
import { MODE_SEGMENTS } from './constants'
import { getChatMessagePostService } from './service'
import { AIModeEnum, ConversationType, ScrollConfigRefType } from './types'
import { readStream } from './utils'

import './styles.scss'

const EmptyStateIcon = () => (
    <div className="enable-svg-animation--infinite flex">
        <Icon name="ic-devtron-ai" color={null} size={48} />
    </div>
)

const AIChat = () => {
    const { aiAgentContext } = useMainContext()
    const { context, path } = aiAgentContext ?? { context: {}, path: '' }

    const [mode, setMode] = useState<AIModeEnum>(AIModeEnum.DEBUG)
    const [inputText, setInputText] = useState('')
    const [conversations, setConversations] = useState<ConversationType[]>([])
    const [lastAnimationTime, setLastAnimationTime] = useState<string>('')
    const [isAIThinking, setIsAIThinking] = useState(false)

    const animationControl = useAnimationControls()

    const sendButtonRef = useRef<HTMLButtonElement>(null)
    const scrollContainerRef = useRef<HTMLDivElement>(null)
    const scrollConfigRef = useRef<ScrollConfigRefType>({
        scrollWasTriggeredProgrammatically: false,
        userHasControlOnScroll: false,
    })

    const { email } = useUserEmail()

    const abortControllerRef = useRef<AbortController>(new AbortController())

    const service = useMemo(() => getChatMessagePostService(email), [])

    const updateLastAnimationTime = () => {
        // NOTE: the accordion opening will trigger onScroll and set userHasControlOnScroll to true
        // For auto scroll to work, we need to reset this flag
        scrollConfigRef.current.userHasControlOnScroll = false
        // NOTE: we need this state to update the key whenever the open animation on accordion completes
        setLastAnimationTime(new Date().toISOString())
    }

    const textareaRefCallback = (node: HTMLTextAreaElement) => {
        // Attach a keydown event listener each time the textarea is mounted,
        // since the component remounts on key change, a callback ref ensures the listener is always set correctly.
        node?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                sendButtonRef.current?.click()
                e.preventDefault()
            }
        })
    }

    const postUserMessage = useMemo(() => {
        // conversations state holds the chat history
        // and responseId is used to identify the current response being processed.
        // It is incremented each time a new message is sent.
        // This allows the function to append new messages correctly without needing to pass the index explicitly.
        let responseId = conversations.length

        return async (message: string) => {
            scrollConfigRef.current.userHasControlOnScroll = false
            // abort previous request
            abortControllerRef.current.abort()
            abortControllerRef.current = new AbortController()

            try {
                // append a new conversation
                setConversations((prev) => [
                    ...prev,
                    {
                        userMessage: { content: message, time: new Date() },
                        aiResponse: {
                            isAIResponseLoading: true,
                            content: '',
                            time: new Date(),
                            finalOutput: '',
                        },
                    },
                ])

                setIsAIThinking(true)
                const reader = await service({
                    message,
                    abortControllerRef,
                    routePath: path,
                    context,
                    mode,
                })

                await readStream({
                    reader,
                    updateAIResponse: ({ done, message: response }) => {
                        setConversations((prev) => {
                            const newConversations = [...prev]

                            if (!done) {
                                newConversations[responseId].aiResponse = response
                            } else {
                                newConversations[responseId].aiResponse.isAIResponseLoading = false
                            }

                            return newConversations
                        })
                    },
                })
                setIsAIThinking(false)
            } catch (error) {
                setConversations((prev) => {
                    const newConversations = [...prev]

                    newConversations[responseId].aiResponse = {
                        ...(newConversations[responseId].aiResponse ?? {
                            content: '',
                            finalOutput: '',
                            time: new Date(),
                        }),
                        isAIResponseLoading: false,
                        error,
                    }

                    return newConversations
                })
                setIsAIThinking(false)
            }

            // update the responseId
            responseId += 1
        }
    }, [path, context, mode])

    const onSendClick = async () => {
        setInputText('')
        await postUserMessage(inputText)
    }

    const onAbortClick = () => {
        abortControllerRef.current.abort()
    }

    const onInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInputText(e.target.value)
    }

    const scrollMessageIntoViewRefCallback: RefCallback<HTMLDivElement> = (node) => {
        if (node && node.dataset.first === 'true') {
            if (!scrollConfigRef.current.userHasControlOnScroll) {
                scrollConfigRef.current.scrollWasTriggeredProgrammatically = true
                // NOTE: magic number 64 is used to account for flex gap & padding
                scrollContainerRef.current.scrollTo({ top: node.offsetTop - 16, behavior: 'smooth' })
            }
            // eslint-disable-next-line no-param-reassign
            node.dataset.first = 'false'
        }
    }

    const onModeChange: SegmentedControlProps['onChange'] = (selected) => {
        setMode(selected.value as AIModeEnum)
    }

    const getRetryFailedPromptHandler = (userMessage: string) => async () => {
        await postUserMessage(userMessage)
    }

    const onScroll = () => {
        if (scrollConfigRef.current.scrollWasTriggeredProgrammatically) {
            scrollConfigRef.current.userHasControlOnScroll = false
            scrollConfigRef.current.scrollWasTriggeredProgrammatically = false
            animationControl
                .start({
                    scale: 0,
                })
                .catch(noop)
            return
        }

        const cutoffContentHeight =
            scrollContainerRef.current.scrollHeight -
            scrollContainerRef.current.scrollTop -
            scrollContainerRef.current.clientHeight

        scrollConfigRef.current.userHasControlOnScroll = cutoffContentHeight > 2 // Floating point tolerance

        animationControl
            .start({
                scale: cutoffContentHeight > 32 ? 1 : 0,
            })
            .catch(noop)
    }

    const onScrollToBottomClick = () => {
        scrollContainerRef.current.scrollTo({
            top: scrollContainerRef.current.scrollHeight - scrollContainerRef.current.clientHeight + 1,
            behavior: 'smooth',
        })
    }

    const renderConversations = () => {
        if (conversations.length === 0) {
            return (
                <GenericEmptyState
                    title="Devtron Ask"
                    subTitle="AI assistant for Devtron insights and debugging help"
                    SvgImage={EmptyStateIcon}
                    styles={{ gap: '16px' }}
                />
            )
        }

        return conversations.map((conversation, index) => {
            const { userMessage, aiResponse } = conversation
            const { content: userMessageContent } = userMessage
            const isLatestPrompt = index === conversations.length - 1

            return (
                // eslint-disable-next-line react/no-array-index-key
                <Fragment key={index}>
                    <div
                        ref={isLatestPrompt ? scrollMessageIntoViewRefCallback : null}
                        className="border__secondary-translucent dc__white-space-pre-wrap bg__tertiary fs-13 lh-1-5 cn-9 py-8 px-12 br-12 ml-20 dc__align-self-end"
                        data-first="true"
                        // NOTE: Adding a key here ensures a new DOM element is created when aiResponse updates.
                        // This resets the data-first attribute, allowing scroll-into-view to work correctly.
                        key={`${JSON.stringify(aiResponse ?? {})}-${isLatestPrompt ? lastAnimationTime : ''}`}
                    >
                        {userMessage.content}
                    </div>

                    {aiResponse && (
                        <AIResponse
                            aiResponse={aiResponse}
                            onRetryClick={getRetryFailedPromptHandler(userMessageContent)}
                            updateLastAnimationTime={isLatestPrompt ? updateLastAnimationTime : noop}
                        />
                    )}
                </Fragment>
            )
        })
    }

    return (
        <div className="ai-chat flexbox-col bg__primary flex-grow-1 dc__overflow-hidden dc__open-sans">
            <div className="flexbox-col flex-grow-1 dc__overflow-hidden dc__position-rel">
                <div
                    onScroll={onScroll}
                    ref={scrollContainerRef}
                    className="flex-grow-1 flexbox-col dc__gap-16 p-16 ai-chat__conversations"
                >
                    {renderConversations()}
                </div>

                <motion.div
                    initial={{ scale: 0 }}
                    animate={animationControl}
                    className="flex dc__position-abs dc__bottom-12 bg__primary br-8 dc__overflow-hidden shadow__card--20 border__primary-translucent"
                    // NOTE: at medium size the button is 32px so offsetting it by half
                    // not using transform because that will be overridden by framer-motion
                    style={{ left: 'calc(50% - 16px)' }}
                >
                    <Button
                        dataTestId="ai-chat__scroll-to-bottom"
                        icon={<Icon name="ic-arrow-right" color={null} rotateBy={90} />}
                        size={ComponentSizeType.medium}
                        variant={ButtonVariantType.borderLess}
                        style={ButtonStyleType.neutral}
                        onClick={onScrollToBottomClick}
                        ariaLabel="Scroll to bottom"
                        showAriaLabelInTippy={false}
                    />
                </motion.div>
            </div>

            <div className="flexbox-col ml-12 mb-12 mr-12 br-6 border__primary shadow__card--10 bg__secondary">
                <div className="flex-grow-1 py-9 px-7">
                    <Textarea
                        key={conversations.length}
                        textareaRef={textareaRefCallback}
                        size={ComponentSizeType.small}
                        name="ai-chat__input"
                        onChange={onInputChange}
                        fullWidth
                        autoFocus
                        value={inputText}
                        placeholder="Ask Devtron..."
                        disableResize
                        borderConfig={{ top: false, right: false, bottom: false, left: false }}
                        newlineOnShiftEnter
                    />
                </div>

                <div className="flexbox dc__content-space dc__gap-6 p-12 dc__align-items-center">
                    {!isAIThinking ? (
                        <SegmentedControl
                            name="ai-chat-mode-toggle"
                            segments={MODE_SEGMENTS}
                            onChange={onModeChange}
                            value={mode}
                            size={ComponentSizeType.small}
                        />
                    ) : (
                        <span className="fs-12 dc__open-sans lh-1-5 cn-6 dc__truncate">AI is thinking</span>
                    )}

                    {isAIThinking && !inputText ? (
                        <Button
                            key="cancel-prompt"
                            icon={<Icon name="ic-stop-fill" color={null} />}
                            dataTestId="ai-chat__cancel"
                            size={ComponentSizeType.small}
                            ariaLabel="Cancel current prompt"
                            onClick={onAbortClick}
                            showAriaLabelInTippy={false}
                            style={ButtonStyleType.neutral}
                            variant={ButtonVariantType.secondary}
                        />
                    ) : (
                        <Button
                            key="send-prompt"
                            ref={sendButtonRef}
                            icon={<Icon name="ic-arrow-right" color={null} rotateBy={270} />}
                            dataTestId="ai-chat__send"
                            size={ComponentSizeType.small}
                            ariaLabel="Send prompt"
                            onClick={onSendClick}
                            disabled={!inputText}
                            showAriaLabelInTippy={false}
                        />
                    )}
                </div>
            </div>
        </div>
    )
}

export default AIChat
