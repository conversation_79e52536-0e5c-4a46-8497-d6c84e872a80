import React, { Dispatch } from 'react'

import { APIOptions } from '@devtron-labs/devtron-fe-common-lib'

export interface AIResponseDTO {
    role: 'model' | 'user' | 'system'
    content: string
    timestamp: string
    type: 'PROGRESS' | 'END'
}

export type MessageType = {
    content: string
    time: Date
}

export type AIResponseType = MessageType & {
    isAIResponseLoading: boolean
    finalOutput: string
    error?: any
}

export interface ConversationType {
    aiResponse: AIResponseType
    userMessage: MessageType
}

type UpdateAIResponseProps =
    | {
          message: AIResponseType
          done?: never
      }
    | {
          done: true
          message?: never
      }

export interface ReadStreamProps {
    reader: ReadableStreamDefaultReader<string>
    updateAIResponse: (props: UpdateAIResponseProps) => void
}

export const enum AIModeEnum {
    DEBUG = 'debug',
    KNOWLEDGE_BASE = 'knowledge_base',
}

export interface AIAgentRequestBodyType {
    session_id: string
    messages: string
    email_id: string
    context_data: Record<string, string>
    uri: string
    mode: AIModeEnum
}

export interface PostMessageServiceProps {
    message: string
    abortControllerRef: APIOptions['abortControllerRef']
    routePath: string
    context: AIAgentRequestBodyType['context_data']
    mode: AIModeEnum
}

export interface ThoughtBubbleProps {
    showThoughBubble: boolean
    setShowThoughtBubble: Dispatch<React.SetStateAction<boolean>>
    isThinking: boolean
    error: any
    onRetryClick: () => void
    updateLastAnimationTime: () => void
}

export interface AIResponseProps extends Pick<ThoughtBubbleProps, 'onRetryClick' | 'updateLastAnimationTime'> {
    aiResponse: AIResponseType
}

export interface ScrollConfigRefType {
    scrollWasTriggeredProgrammatically: boolean
    userHasControlOnScroll: boolean
}
