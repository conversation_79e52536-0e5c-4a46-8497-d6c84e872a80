/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// CODE MIRROR OVERRIDES
.cm-mergeView {
    height: 100%;
    background-color: var(--bg-code-editor-base);
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }
}

.cm-merge-theme:not(.code-editor__mini-map) {
    .cm-mergeViewEditor:first-child {
        border-right: 1px solid var(--N200);
    }

    &:not(.code-editor__read-only) .cm-mergeViewEditor:last-child {
        border-left: 1px solid var(--N200);
    }

    &:not(.code-editor__read-only) .cm-mergeView {
        position: relative;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: calc(50%);
            transform: translate(-50%);
            width: 21px;
            border-left: 1px solid var(--N200);
            border-right: 1px solid var(--N200);
            background-color: var(--bg-primary);
        }
    }

    &.code-editor__read-only .cm-mergeView {
        position: relative;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: calc(50%);
            transform: translate(-50%);
            width: 1px;
            background-color: var(--N200);
        }
    }
}

.cm-scroller {
    min-height: inherit;
}

.cm-merge-revert {
    background-color: var(--bg-primary);

    button {
        margin-top: 4px;
        padding: 0;
    }
}

.cm-editor {
    --indent-marker-bg-color: var(--N200);
    --indent-marker-active-bg-color: var(--N300);

    &.cm-focused {
        outline: none;
    }

    &.cm-merge-a .cm-changedText,
    .cm-deletedChunk .cm-deletedText,
    &.cm-merge-b .cm-changedText {
        border-radius: 4px;
        padding: 0 2px;
        color: var(--N900);

        > span {
            color: var(--N900);
        }
    }

    &.cm-merge-a .cm-changedLine,
    .cm-deletedChunk {
        background-color: var(--bg-code-editor-red);
    }

    &.cm-merge-a .cm-changedText,
    .cm-deletedChunk .cm-deletedText {
        background: var(--bg-code-editor-red-highlight);
    }

    &.cm-merge-a .cm-changedLineGutter {
        background: var(--bg-code-editor-red-gutter);
    }

    &.cm-merge-b .cm-changedLine {
        background: var(--bg-code-editor-green);
    }

    &.cm-merge-b .cm-changedText {
        background: var(--bg-code-editor-green-highlight);
    }

    &.cm-merge-b .cm-changedLineGutter {
        background: var(--bg-code-editor-green-gutter);
    }

    .cm-panels {
        background-color: transparent;
        z-index: 1;
    }

    .cm-panels-top {
        border-bottom: none;

        &:has(.code-editor__search) {
            width: fit-content;
            margin-left: auto;
        }
    }

    // TOOLTIPS
    .cm-tooltip {
        border-radius: 4px;
        border: none;
        background: none;
        color: inherit;

        &:has(.cm-diagnostic-error) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        &:has(.code-editor__schema-tooltip) {
            .cm-tooltip-lint {
                border-bottom-right-radius: 0;
            }

            .cm-diagnostic-error {
                border-bottom-left-radius: 0;
            }
        }

        &:has(.cm-tooltip-lint) .code-editor__schema-tooltip {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
    }

    .cm-tooltip-section:not(:first-child) {
        border-top: 1px solid var(--N100);
    }

    .cm-tooltip-lint {
        color: var(--white);
        background-color: var(--bg-tooltip-black);
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        max-width: 300px;
    }

    .cm-tooltip-autocomplete {
        padding: 2px;
        background-color: var(--bg-menu-primary);
        border: 1px solid var(--border-primary-translucent);

        & > ul > li {
            display: flex;
            gap: 6px;
            padding: 2px 4px;
            color: var(--N900);
            border-radius: 2px;
            font-size: 13px;
            line-height: 20px;
        }

        & > ul li[aria-selected] {
            color: var(--N900);
            background-color: var(--bg-hover);
        }

        .cm-completionDetail {
            margin-left: 0;
        }

        .cm-completionIcon.cm-completionIcon-property {
            box-sizing: border-box;
            height: 14px;
            width: 14px;
            padding-right: 0;
            font-size: 100%;
            vertical-align: middle;
            opacity: 1;

            &::after {
                content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='rgb(29, 39, 48)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M12 22.015a.75.75 0 0 1-.368-.097l-8.25-4.64A.75.75 0 0 1 3 16.623V7.376a.75.75 0 0 1 .382-.654l8.25-4.64a.75.75 0 0 1 .736 0l8.25 4.64a.75.75 0 0 1 .382.654v9.248a.75.75 0 0 1-.382.654l-8.25 4.64a.75.75 0 0 1-.368.097Zm0 0L12.089 12m8.808-5.004L12.089 12m0 0L3.104 6.995' vector-effect='non-scaling-stroke'/%3E%3C/svg%3E");
            }
        }
    }

    .cm-tooltip .cm-completionInfo {
        padding: 3px 7px;
        background-color: var(--bg-menu-primary);
        color: var(--N700);
        border: 1px solid var(--border-primary-translucent);
        border-radius: 0;
        font-size: 13px;
        line-height: 1.5;
        max-height: 240px;
        overflow-y: auto;
    }

    .cm-diagnostic-error {
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        border-left: 2px solid var(--R500);
        line-height: 18px;
    }

    .cm-diagnosticText {
        font-size: 12px;
    }

    .cm-diagnosticSource {
        font-size: 10px;
        line-height: 15px;
        opacity: 0.8;
    }

    // GUTTERS
    .cm-gutters {
        border: none;

        // omitting first child since it is internally used by codemirror
        &:hover .cm-foldGutter .cm-gutterElement:not(:first-child) span {
            visibility: visible;
        }
    }

    .cm-changeGutter {
        position: absolute;
        width: 100%;
        z-index: -1;
    }

    .cm-foldGutter .cm-gutterElement:not(:first-child) span:not(.is-closed) {
        visibility: hidden;
    }

    .cm-gutter-lint {
        width: 16px;
    }

    .cm-gutter-lint .cm-gutterElement {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .cm-lint-marker {
        width: 12px;
        height: 12px;
    }

    .cm-lint-marker-error {
        content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(2.667 2.667)'%3E%3Crect width='26.667' height='26.667' class='fill-color' fill='%23f33e3e' rx='13.333'/%3E%3Cpath fill='%23FFF' fill-rule='nonzero' d='M8.62 8.62c.52-.521 1.364-.521 1.885 0l2.828 2.827 2.829-2.828a1.334 1.334 0 0 1 1.777-.097l.108.097c.521.521.521 1.365 0 1.886l-2.828 2.828 2.828 2.829c.486.486.519 1.254.098 1.777l-.098.108c-.52.521-1.365.521-1.885 0l-2.83-2.828-2.827 2.828a1.334 1.334 0 0 1-1.777.098l-.109-.098a1.333 1.333 0 0 1 0-1.885l2.828-2.83-2.828-2.827a1.334 1.334 0 0 1-.097-1.777z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .cm-lint-marker-warning {
        content: url("data:image/svg+xml,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.24239 2.18666L1.43119 10.4987C1.3542 10.6317 1.3136 10.7827 1.31348 10.9364C1.31335 11.09 1.3537 11.241 1.43046 11.3742C1.50723 11.5073 1.6177 11.6179 1.75077 11.6947C1.88384 11.7716 2.0348 11.8121 2.18848 11.8121H11.8109C11.9646 11.8121 12.1155 11.7716 12.2486 11.6947C12.3817 11.6179 12.4921 11.5073 12.5689 11.3742C12.6457 11.241 12.686 11.09 12.6859 10.9364C12.6858 10.7827 12.6452 10.6317 12.5682 10.4987L7.75697 2.18666C7.68011 2.05387 7.56968 1.94363 7.43676 1.86699C7.30384 1.79034 7.15311 1.75 6.99968 1.75C6.84625 1.75 6.69551 1.79034 6.5626 1.86699C6.42968 1.94363 6.31925 2.05387 6.24239 2.18666Z' fill='%23F4BA63'/%3E%3Cpath d='M7.58333 5.68758C7.58333 5.36542 7.32217 5.10425 7 5.10425C6.67783 5.10425 6.41667 5.36542 6.41667 5.68758V7.87508C6.41667 8.19725 6.67783 8.45841 7 8.45841C7.32217 8.45841 7.58333 8.19725 7.58333 7.87508V5.68758Z' fill='%23000A14'/%3E%3Cpath d='M7.65625 9.84383C7.65625 10.2063 7.36244 10.5001 7 10.5001C6.63756 10.5001 6.34375 10.2063 6.34375 9.84383C6.34375 9.48139 6.63756 9.18758 7 9.18758C7.36244 9.18758 7.65625 9.48139 7.65625 9.84383Z' fill='%23000A14'/%3E%3C/svg%3E%0A");
    }

    // COLLAPSED
    .cm-collapsedLines {
        padding: 6px 12px;
        background: var(--B50);
        font-size: 14px;
        line-height: 20px;
        color: var(--B500);

        &::before,
        &::after {
            content: none;
        }
    }

    // SEARCH
    .cm-searchMatch {
        background-color: var(--bg-matching-keyword);

        span {
            color: var(--white);
        }
    }

    .cm-searchMatch-selected {
        background-color: var(--bg-matching-keyword-selected);

        span {
            color: var(--white);
        }
    }
}

// THEME SPECIFIC STYLES
.component-specific-theme__dark {
    .cm-editor {
        .cm-tooltip-autocomplete .cm-completionIcon.cm-completionIcon-property::after {
            content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='rgb(228, 229, 230)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M12 22.015a.75.75 0 0 1-.368-.097l-8.25-4.64A.75.75 0 0 1 3 16.623V7.376a.75.75 0 0 1 .382-.654l8.25-4.64a.75.75 0 0 1 .736 0l8.25 4.64a.75.75 0 0 1 .382.654v9.248a.75.75 0 0 1-.382.654l-8.25 4.64a.75.75 0 0 1-.368.097Zm0 0L12.089 12m8.808-5.004L12.089 12m0 0L3.104 6.995' vector-effect='non-scaling-stroke'/%3E%3C/svg%3E");
        }

        .cm-lint-marker-error {
            content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g fill="none" fill-rule="evenodd"><g transform="translate(2.667 2.667)"><rect width="26.667" height="26.667" class="fill-color" fill="%23FF5D60" rx="13.333"/><path fill="%23FFF" fill-rule="nonzero" d="M8.62 8.62c.52-.521 1.364-.521 1.885 0l2.828 2.827 2.829-2.828a1.334 1.334 0 0 1 1.777-.097l.108.097c.521.521.521 1.365 0 1.886l-2.828 2.828 2.828 2.829c.486.486.519 1.254.098 1.777l-.098.108c-.52.521-1.365.521-1.885 0l-2.83-2.828-2.827 2.828a1.334 1.334 0 0 1-1.777.098l-.109-.098a1.333 1.333 0 0 1 0-1.885l2.828-2.83-2.828-2.827a1.334 1.334 0 0 1-.097-1.777z"/></g></g></svg>');
        }

        .cm-lint-marker-warning {
            content: url('data:image/svg+xml,<svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.702 3.74857L2.45423 17.9978C2.32226 18.2259 2.25266 18.4846 2.25244 18.748C2.25222 19.0115 2.32139 19.2703 2.45299 19.4986C2.58459 19.7268 2.77397 19.9163 3.00209 20.0481C3.2302 20.1799 3.489 20.2493 3.75245 20.2493H20.248C20.5114 20.2493 20.7702 20.1799 20.9984 20.0481C21.2265 19.9163 21.4159 19.7268 21.5474 19.4986C21.679 19.2703 21.7482 19.0115 21.748 18.748C21.7478 18.4846 21.6782 18.2258 21.5462 17.9978L13.2984 3.74857C13.1667 3.52093 12.9774 3.33193 12.7495 3.20055C12.5216 3.06916 12.2632 3 12.0002 3C11.7372 3 11.4788 3.06916 11.2509 3.20055C11.0231 3.33193 10.8338 3.52093 10.702 3.74857Z" fill="%23F4BA63"/><path d="M13 9.75C13 9.19772 12.5523 8.75 12 8.75C11.4477 8.75 11 9.19772 11 9.75V13.5C11 14.0523 11.4477 14.5 12 14.5C12.5523 14.5 13 14.0523 13 13.5V9.75Z" fill="%23000A14"/><path d="M13.125 16.875C13.125 17.4963 12.6213 18 12 18C11.3787 18 10.875 17.4963 10.875 16.875C10.875 16.2537 11.3787 15.75 12 15.75C12.6213 15.75 13.125 16.2537 13.125 16.875Z" fill="%23000A14"/></svg>');
        }
    }
}

// MINIMAP STYLES
.code-editor__mini-map {
    user-select: none;
    pointer-events: none;

    .cm-editor {
        font-size: inherit;

        &.cm-merge-a .cm-changedLine,
        .cm-deletedChunk,
        &.cm-merge-a .cm-changedText,
        .cm-deletedChunk .cm-deletedText,
        &.cm-merge-a .cm-changedLineGutter {
            background: var(--R500);
        }

        &.cm-merge-b .cm-changedLine,
        &.cm-merge-b .cm-changedText,
        &.cm-merge-b .cm-changedLineGutter {
            background: var(--G500);
        }
    }

    .cm-content {
        padding: 0;
    }

    & .cm-line {
        color: transparent !important;
        padding: 0;

        & span {
            color: transparent !important;
        }
    }

    & .cm-scroller {
        overflow: hidden !important;
    }
}

// CODE EDITOR STYLES
.code-editor {
    &__schema-tooltip {
        background-color: var(--bg-tooltip-black);

        &__source {
            color: var(--button-text-on-dark);
        }
    }

    &__container {
        .cm-merge-theme:not(.code-editor__mini-map) .cm-mergeView {
            border-radius: 4px;
        }

        .code-editor__minimap-container {
            border-bottom-right-radius: 4px;
        }

        .cm-editor {
            border-radius: 4px;

            .cm-scroller {
                border-radius: 4px;
            }
        }

        .cm-merge-theme.code-editor__mini-map {
            .cm-editor {
                border-radius: 0;

                .cm-scroller {
                    border-radius: 0;
                }
            }
        }

        &:has(> [data-code-editor-header]) {
            .cm-editor {
                border-top-right-radius: 0;
                border-top-left-radius: 0;

                .cm-scroller {
                    border-top-right-radius: 0;
                    border-top-left-radius: 0;
                }
            }
        }
    }

    &__header {
        .radio-group {
            height: 24px;
            overflow: hidden;
        }

        .radio-group input[type='checkbox']:checked + .radio__item-label {
            background: var(--N700);
            border-radius: 0;
            color: var(--N0);
        }

        .radio__item-label {
            padding: 0 8px;
            display: inline-block;
            width: 100%;
            height: 100%;
            line-height: 24px;
            cursor: pointer;
        }

        .radio-group {
            padding: 0;
        }

        label.form__error {
            margin-left: 8px;
        }

        button.clipboard {
            margin-left: auto;
            height: 20px;
            width: 20px;
            padding: 0;
            background: transparent;
            border: unset;
            outline: unset;

            svg {
                height: 100%;
                width: 100%;
            }

            &:active {
                svg {
                    height: 90%;
                    width: 90%;

                    path {
                        fill: var(--G500);
                    }
                }
            }
        }
    }

    &__status-info-icon {
        width: 16px;
        height: 16px;
        margin: 0 8px 0 0;
        vertical-align: bottom;
    }

    &__shebang {
        background-color: var(--bg-code-editor-base);

        &__gutter {
            background-color: var(--bg-code-editor-base-gutter);
        }
    }

    &__search {
        &__field-container {
            display: flex;
            align-items: center;
            gap: 4px;
            width: 220px;
            padding: 3px 5px;
            border-radius: 4px;
            border: 1px solid var(--N200);
            line-height: 18px;

            &:focus-within:not(:has(div:focus)) {
                border-color: var(--B500);
            }
        }

        &__replace-expand-button {
            > div {
                display: flex;
            }
        }
    }

    &__read-only-tooltip {
        background-color: var(--bg-tooltip-black);
    }

    &__minimap-container {
        width: 30px;
        background-color: var(--bg-code-editor-base);
    }

    &__minimap-overlay {
        background-color: var(--bg-code-editor-diffmap-scrollbar);
        cursor: grab;
    }
}
