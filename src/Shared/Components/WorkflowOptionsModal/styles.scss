.workflow-options-modal {
    &__header {
        background: linear-gradient(269deg, var(--B100, #{var(--B100)}) 0%, var(--bg-primary, #{var(--white)}) 99.9%);
    }

    &__img {
        top: 13px;
    }

    &__cards-container {
        grid-template-columns: 1fr 1fr 1fr;
    }
}

.source-type-card {
    &__icons {
        transition:
            margin 0.2s,
            padding 0.2s;
    }

    &:hover {
        box-shadow: var(--shadow-10);

        .source-type-card__icons {
            margin: 0;
            padding: 20px 16px;
        }
    }
}
