import { useEffect, useMemo } from 'react'
import ReactGA from 'react-ga4'
import { GroupBase, OptionsOrGroups } from 'react-select'

import {
    ComponentSizeType,
    DeploymentStrategyType,
    DeploymentStrategyTypeWithDefault,
    getSelectPickerOptionByValue,
    Icon,
    SelectPicker,
    SelectPickerOptionType,
    STRATEGY_TYPE_TO_TITLE_MAP,
    useAsync,
} from '@devtron-labs/devtron-fe-common-lib'

import { STRATEGY_TYPE_TO_UNCOLORED_ICON_MAP } from '@Pages/constants'

import { getDeploymentStrategies } from './service'
import { SelectDeploymentStrategyProps } from './types'

const SelectDeploymentStrategy = ({
    pipelineIds,
    deploymentStrategy,
    setDeploymentStrategy,
    isBulkStrategyChange,
    possibleStrategyOptions,
    pipelineStrategiesLoading,
    pipelineStrategiesError,
    reloadPipelineStrategies,
}: SelectDeploymentStrategyProps) => {
    const [strategiesLoading, pipelineStrategies, strategiesError, reloadStrategies] = useAsync(
        () => getDeploymentStrategies(pipelineIds),
        [],
        !!isBulkStrategyChange,
    )

    const strategyOptions: OptionsOrGroups<
        SelectPickerOptionType<DeploymentStrategyTypeWithDefault>,
        GroupBase<SelectPickerOptionType<DeploymentStrategyTypeWithDefault>>
    > = useMemo(() => {
        if (isBulkStrategyChange) {
            // Creating a unique set of possible deployment strategies
            const possibleStrategies: DeploymentStrategyType[] = (pipelineStrategies ?? []).flatMap(
                ({ strategies, error }) => {
                    if (error) {
                        return []
                    }
                    return strategies.map((currStrategy) => currStrategy.deploymentTemplate)
                },
            )

            return [
                {
                    label: '',
                    options: [
                        {
                            label: STRATEGY_TYPE_TO_TITLE_MAP.DEFAULT,
                            value: 'DEFAULT',
                            description: 'Default strategy for selected deployments will be used',
                            startIcon: <Icon name={STRATEGY_TYPE_TO_UNCOLORED_ICON_MAP.DEFAULT} color="N800" />,
                        },
                    ],
                },
                {
                    label: '',
                    options: Array.from(new Set(possibleStrategies)).map(
                        (strategy: DeploymentStrategyTypeWithDefault) => ({
                            label: STRATEGY_TYPE_TO_TITLE_MAP[strategy] ?? strategy,
                            value: strategy,
                            startIcon: (
                                <Icon
                                    name={STRATEGY_TYPE_TO_UNCOLORED_ICON_MAP[strategy] ?? 'ic-rocket-gear'}
                                    color="N800"
                                />
                            ),
                        }),
                    ),
                },
            ]
        }
        return [
            {
                label: 'Deployment Strategy',
                options: (possibleStrategyOptions ?? []).map(({ deploymentTemplate, default: isDefault }) => ({
                    label: `${STRATEGY_TYPE_TO_TITLE_MAP[deploymentTemplate] ?? deploymentTemplate} ${isDefault ? ' (Default)' : ''}`,
                    value: deploymentTemplate,
                    startIcon: (
                        <Icon
                            name={STRATEGY_TYPE_TO_UNCOLORED_ICON_MAP[deploymentTemplate] ?? 'ic-rocket-gear'}
                            color="N800"
                        />
                    ),
                })),
            },
        ]
    }, [pipelineStrategies, possibleStrategyOptions])

    const handleUpdateStrategy = (option: SelectPickerOptionType<DeploymentStrategyTypeWithDefault>) => {
        ReactGA.event({
            category: 'Deployment strategy selection',
            action: isBulkStrategyChange
                ? 'APP_GROUP_DEPLOY_CHANGED_DEPLOYMENT_STRATEGY'
                : 'DA_DEPLOY_CHANGED_DEPLOYMENT_STRATEGY',
        })
        setDeploymentStrategy(option.value)
    }

    useEffect(() => {
        if (!isBulkStrategyChange && possibleStrategyOptions?.length) {
            const defaultStrategy = possibleStrategyOptions.find((strategy) => strategy.default)?.deploymentTemplate

            // selecting default strategy in case of cd modal/ rollback modal
            if (defaultStrategy) {
                setDeploymentStrategy(defaultStrategy)
            }
        }
    }, [possibleStrategyOptions])

    const isLoading = strategiesLoading || pipelineStrategiesLoading

    return (
        <SelectPicker
            inputId="select-deployment-strategy"
            options={strategyOptions}
            size={ComponentSizeType.large}
            isLoading={isLoading}
            isDisabled={isLoading}
            placeholder="Select strategy"
            optionListError={isBulkStrategyChange ? strategiesError : pipelineStrategiesError}
            reloadOptionList={isBulkStrategyChange ? reloadStrategies : reloadPipelineStrategies}
            value={getSelectPickerOptionByValue(strategyOptions, deploymentStrategy, null)}
            onChange={handleUpdateStrategy}
            menuSize={ComponentSizeType.medium}
        />
    )
}

export default SelectDeploymentStrategy
