/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { Dispatch, SetStateAction } from 'react'

import {
    DeploymentStrategyTypeWithDefault,
    ServerError,
    ServerErrors,
    Strategy,
    UseDownloadReturnType,
} from '@devtron-labs/devtron-fe-common-lib'

import { DeploymentManifestDownload } from '@Components/VirtualDeployment/Types'

export type DownloadManifestForVirtualEnvironmentProps = Pick<UseDownloadReturnType, 'handleDownload'> &
    Pick<DeploymentManifestDownload, 'appId' | 'envId' | 'cdWorkflowType'> &
    (
        | (Pick<DeploymentManifestDownload, 'appName'> & { helmPackageName?: never })
        | { appName?: never; helmPackageName: string }
    ) &
    Pick<
        Parameters<UseDownloadReturnType['handleDownload']>['0'],
        'downloadSuccessToastContent' | 'showSuccessfulToast' | 'showFilePreparingToast' | 'fileName'
    >

export type SelectDeploymentStrategyProps = {
    pipelineIds: number[]
    deploymentStrategy: DeploymentStrategyTypeWithDefault
    setDeploymentStrategy: Dispatch<SetStateAction<DeploymentStrategyTypeWithDefault>>
} & (
    | {
          isBulkStrategyChange: false
          possibleStrategyOptions: Strategy[]
          pipelineStrategiesLoading: boolean
          pipelineStrategiesError: ServerErrors | ServerError
          reloadPipelineStrategies: () => void
      }
    | {
          isBulkStrategyChange: true
          possibleStrategyOptions?: never
          pipelineStrategiesLoading?: never
          pipelineStrategiesError?: never
          reloadPipelineStrategies?: never
      }
)
