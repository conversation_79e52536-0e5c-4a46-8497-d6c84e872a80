import { useRef, useState } from 'react'

import {
    APIResponse<PERSON><PERSON><PERSON>,
    Button,
    ButtonStyleType,
    ButtonVariantType,
    ComponentSizeType,
    FormProps,
    HIDE_SUBMIT_BUTTON_UI_SCHEMA,
    Icon,
    Progressing,
    RJSFForm,
    RJSFFormSchema,
    showError,
    stopPropagation,
    ToastManager,
    ToastVariantType,
    useAsync,
    VisibleModal,
} from '@devtron-labs/devtron-fe-common-lib'

import { WebhookAddImageModalProps } from './types'
import { executeWebhookAPI } from './utils'
import webhookFormSchema from './webhook.rjsf.json'

export const WebhookAddImageModal = ({ onClose, getWebhookDetails }: WebhookAddImageModalProps) => {
    // STATES
    const [formData, setFormData] = useState({})
    const [isAddingImage, setIsAddingImage] = useState<boolean>(false)

    // REFS
    const webhookFormRef: FormProps['ref'] = useRef(null)

    // ASYNC CALL - FETCH WEBHOOK DETAILS
    const [isWebhookDetailsLoading, webhookDetailsRes, webhookDetailsErr, reloadWebhookDetails] = useAsync(
        getWebhookDetails,
        [],
    )

    // HANDLERS
    const handleFormChange: FormProps['onChange'] = (data) => {
        setFormData(data.formData)
    }

    const onImageAdd = async () => {
        const isFormValid = !webhookFormRef.current?.validateForm || webhookFormRef.current.validateForm()

        if (!isFormValid) {
            ToastManager.showToast({
                variant: ToastVariantType.error,
                description: 'Some required fields are missing',
            })

            return
        }

        setIsAddingImage(true)
        try {
            await executeWebhookAPI(webhookDetailsRes.result.webhookUrl, formData)
            onClose()
            ToastManager.showToast({
                variant: ToastVariantType.success,
                description: 'Image is added',
            })
        } catch (err) {
            showError(err)
        } finally {
            setIsAddingImage(false)
        }
    }

    return (
        <VisibleModal onEscape={onClose} close={onClose}>
            <div className="bg__primary w-600 mxh-600 dc__m-auto mt-40 flexbox-col br-8" onClick={stopPropagation}>
                <div className="flex dc__content-space border__secondary--bottom px-20 pt-16 pb-15">
                    <h2 className="m-0 fs-16 lh-20 fw-6">Add image (External)</h2>
                    <Button
                        icon={<Icon name="ic-close-small" color={null} />}
                        dataTestId="webhook-add-image-modal-close"
                        ariaLabel="webhook-add-image-modal-close"
                        variant={ButtonVariantType.borderLess}
                        style={ButtonStyleType.negativeGrey}
                        size={ComponentSizeType.xs}
                        showAriaLabelInTippy={false}
                        onClick={onClose}
                    />
                </div>
                <APIResponseHandler
                    isLoading={isWebhookDetailsLoading}
                    error={webhookDetailsErr}
                    errorScreenManagerProps={{ code: webhookDetailsErr?.code, reload: reloadWebhookDetails }}
                    customLoader={
                        <div className="mh-300 flex">
                            <Progressing pageLoader />
                        </div>
                    }
                >
                    <div className="flex-grow-1 dc__overflow-auto">
                        <RJSFForm
                            ref={webhookFormRef}
                            schema={webhookFormSchema as RJSFFormSchema}
                            formData={formData}
                            onChange={handleFormChange}
                            uiSchema={HIDE_SUBMIT_BUTTON_UI_SCHEMA}
                            liveValidate
                        />
                    </div>
                    <div className="flex right dc__gap-12 border__secondary--top px-20 pt-15 pb-16">
                        <Button
                            dataTestId="webhook-add-image-modal-cancel"
                            variant={ButtonVariantType.secondary}
                            style={ButtonStyleType.neutral}
                            size={ComponentSizeType.medium}
                            onClick={onClose}
                            text="Cancel"
                        />
                        <Button
                            dataTestId="webhook-add-image-modal-save"
                            size={ComponentSizeType.medium}
                            isLoading={isAddingImage}
                            disabled={isWebhookDetailsLoading}
                            onClick={onImageAdd}
                            text="Add Image"
                        />
                    </div>
                </APIResponseHandler>
            </div>
        </VisibleModal>
    )
}
