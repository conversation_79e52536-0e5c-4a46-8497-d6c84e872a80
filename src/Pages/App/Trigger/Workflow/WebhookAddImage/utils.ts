import { API_STATUS_CODES, get<PERSON><PERSON><PERSON>, Server<PERSON>rrors, TOKEN_COOKIE_NAME } from '@devtron-labs/devtron-fe-common-lib'

// TODO: replace with core API - later
export const executeWebhookAPI = async (webhookUrl: string, data: object) => {
    const options = {
        method: 'POST',
        headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'Access-Control-Allow-Credentials': 'true',
            'api-token': getCookie(TOKEN_COOKIE_NAME),
        },
        body: JSON.stringify(data),
        credentials: 'include' as RequestCredentials,
    }

    const response = await fetch(webhookUrl, options)
    const responseBody = await response.json()
    if (responseBody?.code !== API_STATUS_CODES.OK) {
        throw new ServerErrors({ code: responseBody.code, errors: responseBody.errors })
    }
    return response
}
