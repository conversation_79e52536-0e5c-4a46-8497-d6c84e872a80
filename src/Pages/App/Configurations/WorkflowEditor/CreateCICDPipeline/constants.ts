export const CREATE_CI_CD_PIPELINE_TOAST_MESSAGES = {
    CREATE_WORKFLOW_SUCCESS: 'Workflow created',
    CREATE_WORKFLOW_FAILED: 'Failed to create workflow. Please retry again.',
    CREATE_CI_SUCCESS_CD_FAILED:
        'Creation of Deployment pipeline failed Please retry to create. Build pipeline is created.',
    SCAN_VALIDATION_FAILED: 'Scanning is mandatory, please enable scanning',
    FORM_VALIDATION_FAILED: 'Please ensure all fields are valid',
}
