.ci-cd-pipeline {
    $stepper-gap: 16px;
    $stepper-connector-left: 18px;

    min-height: 400px;
    max-height: 70vh;

    &__stepper-container {
        gap: #{$stepper-gap};
    }

    &__stepper:not(:last-child) {
        position: relative;

        &::before,
        &::after {
            content: '';
            position: absolute;
            width: 1px;
            background-color: var(--border-secondary-translucent);
        }

        &::before {
            top: 0;
            left: #{$stepper-connector-left};
            height: 100%;
        }

        &::after {
            top: 100%;
            left: #{$stepper-connector-left};
            height: #{$stepper-gap};
        }
    }

    .chartrepo-type__radio-group {
        .form__radio-item .form__radio-item-content {
            padding: 4px 16px 4px 0;
        }
    }
}
