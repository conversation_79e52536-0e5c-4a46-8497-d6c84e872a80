import { Checkbox, CHECKBOX_VALUE, GenericSectionErrorState, useAsync } from '@devtron-labs/devtron-fe-common-lib'

import { getChartCategoryFilterOptions } from './service'
import { ChartCategoryFiltersProps } from './types'

const ChartCategoryFilters = ({ selectedCategories, handleUpdateCategoryFilter }: ChartCategoryFiltersProps) => {
    const [optionsLoading, optionsResult, optionsError, reloadOptions] = useAsync(getChartCategoryFilterOptions, [])

    const handleChangeFilter = (categoryId: string) => {
        const isCategoryPresent = selectedCategories.find((category) => category === categoryId)
        if (isCategoryPresent) {
            const updatedArray = selectedCategories.filter((category) => category !== categoryId)
            handleUpdateCategoryFilter(updatedArray)
            return
        }
        const updatedArray = [...selectedCategories, categoryId]
        handleUpdateCategoryFilter(updatedArray)
    }

    if (optionsResult?.category?.length <= 0) {
        return null
    }

    const renderOptionsList = () => {
        if (optionsLoading) {
            return (
                <div>
                    <span className="w-180 shimmer" />
                    <span className="w-180 shimmer" />
                    <span className="w-180 shimmer" />
                </div>
            )
        }

        if (optionsError) {
            return <GenericSectionErrorState title="Could not load categories" subTitle="" reload={reloadOptions} />
        }

        const getOnChangeHandler = (id: string) => () => handleChangeFilter(id)

        return (
            <>
                {optionsResult.category.map((category) => {
                    const id = category.id.toString()
                    const isChecked = !!selectedCategories.find((currId) => currId === id)
                    return (
                        <div className="dc__hover-n50" key={`${category.name}-${category.id}`}>
                            <Checkbox
                                isChecked={isChecked}
                                value={CHECKBOX_VALUE.CHECKED}
                                onChange={getOnChangeHandler(id)}
                                dataTestId={`chart-store-category-${category.name}-checkbox`}
                                rootClassName="mb-0 py-6 ml-8"
                            >
                                <span className="fs-13 lh-20 dc__truncate">{category.name}</span>
                            </Checkbox>
                        </div>
                    )
                })}
            </>
        )
    }

    return (
        <>
            <hr className="mt-8 mb-8" />
            <span className="fs-12 lh-20 cn-6 fw-6 px-8 py-8">CATEGORY</span>
            {renderOptionsList()}
        </>
    )
}

export default ChartCategoryFilters
