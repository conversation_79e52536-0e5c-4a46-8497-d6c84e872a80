/*
 * Copyright (c) 2024. Devtron Inc.
 */
import { DEFAULT_ENV, TabProps, WorkflowStatusEnum } from '@devtron-labs/devtron-fe-common-lib'

import { URLS } from '@Common/Constants'

import { ExecutionEnvOption, FilterKeys, JobPipelineOptionType } from './Types'

export const JOB_PIPELINE_DEFAULT_OPTION: JobPipelineOptionType[] = [
    {
        value: 0,
        label: 'Not selected',
        description: '',
        jobId: 0,
        jobName: '',
    },
]

export const EXECUTION_ENV_DEFAULT_OPTION: ExecutionEnvOption[] = [
    {
        value: 0,
        label: DEFAULT_ENV,
        clusterId: 0,
        clusterName: '',
        description: '',
    },
    {
        value: -1,
        label: 'Source environment',
        clusterId: 0,
        clusterName: '',
        description:
            'Job will run in intercepted namespace. If not added in Devtron, it runs in "devtron-ci" namespace.',
    },
]

export enum EventWatcherListSortableKeys {
    name = 'name',
    triggeredAt = 'triggeredAt',
}

export enum InterceptedEventListSortableKeys {
    interceptedBy = 'interceptedBy',
    interceptedAt = 'interceptedAt',
    jobExecution = 'jobExecution',
}

export enum CreateEventWatcherTabKeys {
    basicDetails = 'basicDetails',
    namespaceDetails = 'namespaceDetails',
    interceptChanges = 'interceptChanges',
    runbook = 'runbook',
}

export const CREATE_WATCHER_TABS = Object.values(CreateEventWatcherTabKeys)

export const CreateEventWatcherTabs: Record<CreateEventWatcherTabKeys, { title: string; description: string }> = {
    [CreateEventWatcherTabKeys.basicDetails]: {
        title: 'Basic Details',
        description: 'Name & description',
    },
    [CreateEventWatcherTabKeys.namespaceDetails]: {
        title: 'Namespaces to watch',
        description: 'Watches selected namespaces for changes',
    },
    [CreateEventWatcherTabKeys.interceptChanges]: {
        title: 'Intercept change in resources',
        description: 'Watches selected resource kinds and intercept changes',
    },
    [CreateEventWatcherTabKeys.runbook]: {
        title: 'Execute runbook',
        description: 'Executes tasks when matching changes are intercepted',
    },
}

export const WATCH_EVENTS = {
    EMPTY_STATE: {
        TITLE: 'Configure watchers',
        SUB_TITLE: `Create watchers to monitor changes in Kubernetes resources across clusters. Execute a runbook when when a matching change is intercepted.`,
    },
}

export const INTERCEPTED_CHANGES_MESSAGES = {
    EMPTY_STATE: {
        TITLE: 'Changes intercepted by watchers will be available here',
        SUB_TITLE: `Your configured watchers will intercept relevant cluster events here. Stay tuned while Devtron keeps an eye on things for you!`,
    },
}

export enum WatchClusterKeysEnum {
    allClusters = 'ALL',
    specificClusters = 'specificClusters',
}

export const WatchClusterOptions = {
    [WatchClusterKeysEnum.allClusters]: 'All Clusters',
    [WatchClusterKeysEnum.specificClusters]: 'Specific Clusters',
}

export enum IncludeExcludeKeysEnum {
    included = 'INCLUDED',
    excluded = 'EXCLUDED',
}

export const IncludeExcludeValues = {
    [IncludeExcludeKeysEnum.excluded]: 'Exclude selections',
    [IncludeExcludeKeysEnum.included]: 'Include selections',
}

export enum WatchNamespaceKeysEnum {
    allNamespace = 'ALL',
    allProdEnv = 'ALL_PROD',
    allNonProdEnv = 'ALL_NON_PROD',
    specificNamespace = 'specificNamespace',
}

export const WatchNamespaceValues = {
    [WatchNamespaceKeysEnum.allNamespace]: 'All Namespaces',
    [WatchNamespaceKeysEnum.allProdEnv]: 'All Prod Environments',
    [WatchNamespaceKeysEnum.allNonProdEnv]: 'All Non-prod Environments',
    [WatchNamespaceKeysEnum.specificNamespace]: 'Specific Namespaces',
}

export const DUMMY_VALUE_FOR_REQUIRED_FIELD = 'dummy'

export const FilterKeysLabel = {
    [FilterKeys.CLUSTER]: 'Cluster',
    [FilterKeys.NAMESPACE]: 'Namespace',
    [FilterKeys.SELECTED_ACTION]: 'Action',
    [FilterKeys.WATCHER]: 'Watcher',
    [FilterKeys.EXECUTION_STATUS]: 'Execution Status',
}

export enum ActionKeys {
    CREATED = 'CREATED',
    UPDATED = 'UPDATED',
    DELETED = 'DELETED',
}

export const ACTION_LABEL = {
    CREATED: 'Created',
    UPDATED: 'Updated',
    DELETED: 'Deleted',
}

export enum EXECUTION_STATUS_KEYS {
    Succeeded = 'Succeeded',
    Failed = 'Failed',
    Error = 'Error',
    Pending = 'Pending',
    Running = 'Running',
    CANCELLED = 'CANCELLED',
    Starting = 'Starting',
    Progressing = 'Progressing',
    inProgress = 'In progress',
    WaitingToStart = WorkflowStatusEnum.WAITING_TO_START,
    TimedOut = WorkflowStatusEnum.TIMED_OUT,
}

export enum EXECUTION_STATUS_VALUES {
    Succeeded = 'Succeeded',
    Failed = 'Failed',
    Error = 'Error',
    Pending = 'Pending',
    Running = 'Running',
    CANCELLED = 'Aborted',
    Starting = 'Starting',
    Progressing = 'Progressing',
    inProgress = 'In progress',
    WaitingToStart = WorkflowStatusEnum.WAITING_TO_START,
    TimedOut = WorkflowStatusEnum.TIMED_OUT,
}

export const EXECUTION_STATUS_OPTIONS = [
    { label: EXECUTION_STATUS_VALUES.Succeeded, value: EXECUTION_STATUS_KEYS.Succeeded },
    { label: EXECUTION_STATUS_VALUES.Failed, value: EXECUTION_STATUS_KEYS.Failed },
    { label: EXECUTION_STATUS_VALUES.inProgress, value: EXECUTION_STATUS_KEYS.Progressing },
]

export const SELECTED_ACTION_OPTIONS = [
    { label: ACTION_LABEL.CREATED, value: ActionKeys.CREATED },
    { label: ACTION_LABEL.UPDATED, value: ActionKeys.UPDATED },
    { label: ACTION_LABEL.DELETED, value: ActionKeys.DELETED },
]

export const SUB_GROUP_OPTIONS_MAP = {
    [WatchNamespaceKeysEnum.allNamespace]: {
        label: WatchNamespaceValues[WatchNamespaceKeysEnum.allNamespace],
        value: WatchNamespaceKeysEnum.allNamespace,
    },
    [WatchNamespaceKeysEnum.allProdEnv]: {
        label: WatchNamespaceValues[WatchNamespaceKeysEnum.allProdEnv],
        value: WatchNamespaceKeysEnum.allProdEnv,
    },
    [WatchNamespaceKeysEnum.allNonProdEnv]: {
        label: WatchNamespaceValues[WatchNamespaceKeysEnum.allNonProdEnv],
        value: WatchNamespaceKeysEnum.allNonProdEnv,
    },
    [WatchNamespaceKeysEnum.specificNamespace]: {
        label: WatchNamespaceValues[WatchNamespaceKeysEnum.specificNamespace],
        value: WatchNamespaceKeysEnum.specificNamespace,
    },
}

export const SUB_GROUP_OPTIONS = Object.values(SUB_GROUP_OPTIONS_MAP)

export const SELECTION_OPTIONS_MAP = {
    [IncludeExcludeKeysEnum.included]: {
        label: IncludeExcludeValues[IncludeExcludeKeysEnum.included],
        value: IncludeExcludeKeysEnum.included,
    },
    [IncludeExcludeKeysEnum.excluded]: {
        label: IncludeExcludeValues[IncludeExcludeKeysEnum.excluded],
        value: IncludeExcludeKeysEnum.excluded,
    },
}

export const SELECTION_OPTIONS = Object.values(SELECTION_OPTIONS_MAP)

export const URL_SEARCH_PARAM = {
    WATCH_EVENT_DETAIL: 'watcher-detail-id',
    INTERCEPTED_CHANGE_DETAIL: 'resource-detail-id',
}

export const RUNBOOK_VARIABLE_TIPPY = {
    title: 'Available variables',
}

export const NO_JOB_PIPELINE = 'No job pipeline'

enum RESOURCE_WATCHER_TABS_TITLE {
    WATCHERS = 'Watchers',
    INTERCEPTED_CHANGES = 'Intercepted Changes',
}

export const RESOURCE_WATCHER_TABS_CONFIG: TabProps[] = [
    {
        id: 'resource-watcher-watchers-tab',
        label: RESOURCE_WATCHER_TABS_TITLE.WATCHERS,
        tabType: 'navLink',
        props: { to: URLS.WATCHERS, 'data-testid': 'watch-events' },
    },
    {
        id: 'resource-watcher-intercepted-changes-tab',
        label: RESOURCE_WATCHER_TABS_TITLE.INTERCEPTED_CHANGES,
        tabType: 'navLink',
        props: { to: URLS.INTERCEPTED_CHANGES, 'data-testid': 'intercepted-events' },
    },
]

export declare enum ResourceWatcherInterceptedEventsTableColumnKeys {
    RESOURCE = 'resource',
    RESOURCE_KIND = 'resource-kind',
    CLUSTER_NAMESPACE = 'cluster-namespace',
    INTERCEPTED_BY = 'intercepted-by',
    INTERCEPTED_AT = 'intercepted-at',
    EXECUTION_STATUS = 'execution-status',
}
