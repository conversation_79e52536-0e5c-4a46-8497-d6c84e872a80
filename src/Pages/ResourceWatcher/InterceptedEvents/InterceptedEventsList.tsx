/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useMemo, useRef } from 'react'
import { Link, useHistory, useLocation } from 'react-router-dom'
import Tippy from '@tippyjs/react'
import dayjs from 'dayjs'

import {
    abortPreviousRequests,
    Button,
    ButtonVariantType,
    ComponentSizeType,
    ConditionalWrap,
    DEFAULT_BASE_PAGE_SIZE,
    ErrorScreenManager,
    GenericEmptyState,
    GenericFilterEmptyState,
    getClusterListMin,
    Icon,
    IconName,
    ImageType,
    Pagination,
    Progressing,
    SortableTableHeaderCell,
    stopPropagation,
    URLS as CommonLibURL,
    useAsync,
    useUrlFilters,
    ZERO_TIME_STRING,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as ICPending } from '../../../Assets/Icons/ic-clock.svg'
import { ReactComponent as ICError } from '../../../Assets/Icons/ic-error.svg'
import { ReactComponent as ErrorIcon } from '../../../Assets/Icons/ic-errorInfo.svg'
import { ReactComponent as ICInProgress } from '../../../Assets/Icons/ic-in-progress.svg'
import { ReactComponent as LogAnalyzerIcon } from '../../../Assets/Icons/ic-logs.svg'
import { ReactComponent as ICSuccess } from '../../../Assets/Icons/ic-success.svg'
import EmptyEvents from '../../../Assets/Img/empty-intercepted-events.png'
import { DATE_TIME_FORMATS, URLS } from '../../../Common/Constants'
import {
    ActionKeys,
    EXECUTION_STATUS_KEYS,
    EXECUTION_STATUS_VALUES,
    INTERCEPTED_CHANGES_MESSAGES,
    InterceptedEventListSortableKeys,
    URL_SEARCH_PARAM,
} from '../constants'
import { getInterceptedEventList } from '../Service'
import { FilterKeys, InterceptedEventsRowType, RunbookIdentifierType } from '../Types'
import { formatGroupKindName, parseSearchParams } from '../utils'
import EventsDetail from './Modal/EventsDetail'
import InterceptedEventFilters from './InterceptedEventFilters'

const TippyComponent = (children, errorMessage: string) => (
    <Tippy
        className="default-tt"
        placement="top"
        arrow={false}
        content={
            <div className="flex column left fs-12">
                <span className="fw-6">Failed to trigger job pipeline</span>
                <span>{errorMessage}</span>
            </div>
        }
    >
        {children}
    </Tippy>
)

const InterceptedEventsList = () => {
    const location = useLocation()
    const history = useHistory()

    const urlFilters = useUrlFilters<InterceptedEventListSortableKeys, Partial<Record<FilterKeys, string[]>>>({
        initialSortKey: InterceptedEventListSortableKeys.interceptedAt,
        parseSearchParams,
    })
    const {
        pageSize,
        offset,
        searchKey,
        handleSearch,
        clearFilters,
        sortBy,
        sortOrder,
        handleSorting,
        updateSearchParams,
        changePage,
        changePageSize,
        ...filter
    } = urlFilters

    const filterConfig = useMemo(
        () => ({
            size: pageSize,
            offset,
            search: searchKey,
            orderBy: sortBy,
            order: sortOrder,
            [FilterKeys.CLUSTER]: filter[FilterKeys.CLUSTER],
            [FilterKeys.NAMESPACE]: filter[FilterKeys.NAMESPACE],
            [FilterKeys.SELECTED_ACTION]: filter[FilterKeys.SELECTED_ACTION],
            [FilterKeys.WATCHER]: filter[FilterKeys.WATCHER],
            [FilterKeys.EXECUTION_STATUS]: filter[FilterKeys.EXECUTION_STATUS],
        }),
        [
            pageSize,
            offset,
            searchKey,
            sortBy,
            sortOrder,
            JSON.stringify(filter[FilterKeys.CLUSTER]),
            JSON.stringify(filter[FilterKeys.NAMESPACE]),
            JSON.stringify(filter[FilterKeys.SELECTED_ACTION]),
            JSON.stringify(filter[FilterKeys.WATCHER]),
            JSON.stringify(filter[FilterKeys.EXECUTION_STATUS]),
        ],
    )

    const abortControllerRef = useRef(new AbortController())
    const [loading, interceptedEventsList, error, reload] = useAsync(
        () =>
            abortPreviousRequests(
                () => getInterceptedEventList(filterConfig, abortControllerRef.current.signal),
                abortControllerRef,
            ),
        [filterConfig],
        true,
        {
            resetOnChange: false,
        },
    )

    const [clusterListLoading, clusterListResponse] = useAsync(() => getClusterListMin(), [])

    const areFiltersApplied =
        !!searchKey ||
        filterConfig[FilterKeys.CLUSTER].length > 0 ||
        filterConfig[FilterKeys.NAMESPACE].length > 0 ||
        filterConfig[FilterKeys.SELECTED_ACTION].length > 0 ||
        filterConfig[FilterKeys.WATCHER].length > 0 ||
        filterConfig[FilterKeys.EXECUTION_STATUS].length > 0

    const openEditWatcherModal = (id: number) => () => {
        const searchParams = new URLSearchParams(location.search)
        searchParams.set(URL_SEARCH_PARAM.INTERCEPTED_CHANGE_DETAIL, `${id}`)
        history.push({ search: new URLSearchParams(searchParams).toString() })
    }

    const renderEmptyState = (): JSX.Element => {
        if (interceptedEventsList.total === 0 && !areFiltersApplied) {
            return (
                <div className="flex flex-grow-1">
                    <GenericEmptyState
                        image={EmptyEvents}
                        title={INTERCEPTED_CHANGES_MESSAGES.EMPTY_STATE.TITLE}
                        subTitle={INTERCEPTED_CHANGES_MESSAGES.EMPTY_STATE.SUB_TITLE}
                        isButtonAvailable={false}
                        imageType={ImageType.Large}
                    />
                </div>
            )
        }
        return (
            <div className="flex flex-grow-1">
                <GenericFilterEmptyState handleClearFilters={clearFilters} imageType={ImageType.Large} />
            </div>
        )
    }

    const renderEventDetailModal = (): JSX.Element | null => {
        const searchParams = new URLSearchParams(location.search)
        if (!searchParams.get(URL_SEARCH_PARAM.INTERCEPTED_CHANGE_DETAIL)) {
            return null
        }
        return <EventsDetail />
    }

    const renderLogs = (watcherData: InterceptedEventsRowType) => {
        if (
            watcherData.trigger.identifierType === RunbookIdentifierType.DEVTRON_JOB &&
            watcherData.triggerExecutionId &&
            watcherData.trigger.data
        ) {
            return (
                <Link
                    className="anchor dc__ellipsis-right-right flex top"
                    to={`${CommonLibURL.JOB}/${watcherData.trigger.data.jobId}/${CommonLibURL.APP_CI_DETAILS}/${watcherData.trigger.data.pipelineId}/${watcherData.triggerExecutionId}/${CommonLibURL.LOGS}`}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <LogAnalyzerIcon className="icon-dim-16 fcb-5 mt-4 mr-6" />
                    logs
                </Link>
            )
        }
        return '-'
    }

    const getResourceKind = (metaData: string) => {
        if (metaData) {
            try {
                const _involvedObject = JSON.parse(metaData.toLowerCase())
                return _involvedObject.kind
            } catch {
                // Do nothing
            }
        }
        return '-'
    }

    const formatInvolvedObject = (metadata: string, clusterId: number) => {
        if (metadata) {
            try {
                const _involvedObject = JSON.parse(metadata.toLowerCase())
                const { group, kind, name, namespace } = _involvedObject
                const _group = group || 'k8sEmptyGroup'
                const _namespace = namespace || 'all'

                const _url = `${URLS.RESOURCE_BROWSER}/${clusterId}/${_namespace}/${kind}/${_group}/${name}`
                const formattedGroupKindName = formatGroupKindName('', kind, name)
                return (
                    <Tippy className="default-tt" arrow={false} placement="top" content={formattedGroupKindName}>
                        <Link className="anchor dc__truncate" to={_url} target="_blank" rel="noopener noreferrer">
                            {formattedGroupKindName}
                        </Link>
                    </Tippy>
                )
            } catch {
                // Do nothing
            }
        }
        return '-'
    }

    const formatInterceptedAt = (interceptedTime: string) => {
        if (interceptedTime && interceptedTime !== ZERO_TIME_STRING) {
            return dayjs(interceptedTime).format(DATE_TIME_FORMATS.TWELVE_HOURS_FORMAT)
        }
        return '-'
    }

    const getIcon = (status: EXECUTION_STATUS_KEYS) => {
        let StatusIcon
        let iconClass = 'icon-dim-16 mt-2 mr-6'
        switch (status) {
            case EXECUTION_STATUS_KEYS.Succeeded:
                StatusIcon = ICSuccess
                break
            case EXECUTION_STATUS_KEYS.Failed:
            case EXECUTION_STATUS_KEYS.CANCELLED:
            case EXECUTION_STATUS_KEYS.TimedOut:
                StatusIcon = ICError
                break
            case EXECUTION_STATUS_KEYS.Error:
                StatusIcon = ErrorIcon
                break
            case EXECUTION_STATUS_KEYS.Pending:
                StatusIcon = ICPending
                break
            case EXECUTION_STATUS_KEYS.Running:
            case EXECUTION_STATUS_KEYS.Progressing:
            case EXECUTION_STATUS_KEYS.Starting:
            case EXECUTION_STATUS_KEYS.WaitingToStart:
                StatusIcon = ICInProgress
                iconClass += ' ic-in-progress-orange'
                break
            default:
                StatusIcon = null
        }
        return StatusIcon && <StatusIcon className={iconClass} />
    }

    const formatExecutionStatus = (status: EXECUTION_STATUS_KEYS, executionMessage: string) => (
        <ConditionalWrap
            condition={status === EXECUTION_STATUS_KEYS.Error && !!executionMessage}
            wrap={(child) => TippyComponent(child, executionMessage)}
        >
            <div className="lh-20 flex left top">
                {getIcon(status)}
                {EXECUTION_STATUS_VALUES[status]}
            </div>
        </ConditionalWrap>
    )

    const renderListBody = (): JSX.Element => (
        <div>
            {interceptedEventsList?.list?.map((interceptedEventData) => (
                <div
                    key={interceptedEventData.interceptedEventId}
                    className="intercepted-event-list__row fw-4 cn-7 fs-13 pt-12 pb-12 pr-20 pl-20 dc__border-bottom-n1 dc__hover-n50 dc__visible-hover dc__visible-hover--parent"
                    onClick={stopPropagation}
                >
                    <div className="flex dc__gap-16 left dc__align-start">
                        <Icon
                            name={
                                interceptedEventData.action === ActionKeys.CREATED
                                    ? 'ic-diff-added'
                                    : (`ic-diff-${interceptedEventData.action.toLowerCase()}` as IconName)
                            }
                            size={20}
                            color={null}
                        />
                        <div className="flexbox dc__gap-12">
                            {formatInvolvedObject(interceptedEventData.metadata, interceptedEventData.clusterId)}
                            <div className="dc__link dc__visible-hover--child pointer">
                                <Button
                                    dataTestId={interceptedEventData.watcherName}
                                    onClick={openEditWatcherModal(interceptedEventData.interceptedEventId)}
                                    text="View Changes"
                                    variant={ButtonVariantType.text}
                                    size={ComponentSizeType.small}
                                    startIcon={<Icon name="ic-file-code" color={null} size={16} />}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="lh-20">{getResourceKind(interceptedEventData.metadata)}</div>
                    <div className="lh-20 dc__ellipsis-right">
                        <Tippy
                            className="default-tt"
                            arrow={false}
                            placement="top"
                            content={
                                <div className="flex column left fs-12">
                                    <span className="fw-6">Env: {interceptedEventData.environmentName}</span>
                                    <span>{`${interceptedEventData.clusterName}/${interceptedEventData.namespace}`}</span>
                                </div>
                            }
                        >
                            <span>{`${interceptedEventData.clusterName}/${interceptedEventData.namespace}`}</span>
                        </Tippy>
                    </div>
                    <div className="lh-20 dc__ellipsis-right">
                        <Tippy
                            className="default-tt"
                            arrow={false}
                            placement="top"
                            content={interceptedEventData.watcherName}
                        >
                            <span>{interceptedEventData.watcherName}</span>
                        </Tippy>
                    </div>
                    <div className="lh-20">{formatInterceptedAt(interceptedEventData.interceptedTime)}</div>
                    {formatExecutionStatus(interceptedEventData.executionStatus, interceptedEventData.executionMessage)}
                    {renderLogs(interceptedEventData)}
                </div>
            ))}
        </div>
    )

    const sortByInterceptedAt = () => {
        handleSorting(InterceptedEventListSortableKeys.interceptedAt)
    }

    const renderListHeader = (): JSX.Element => (
        <div className="intercepted-event-list__row fw-6 cn-7 fs-12 dc__border-bottom-n1 pt-6 pb-6 pr-20 pl-20 dc__uppercase h-36 dc__position-sticky dc__top-0 bg__primary">
            <div className="h-24 lh-24">Change in resource</div>
            <div className="h-24 lh-24">Resource Kind</div>
            <div className="h-24 lh-24">Cluster/Namespace</div>
            <div className="h-24 lh-24">Intercepted by</div>
            <SortableTableHeaderCell
                title="Intercepted at"
                triggerSorting={sortByInterceptedAt}
                isSorted={sortBy === InterceptedEventListSortableKeys.interceptedAt}
                sortOrder={sortOrder}
                disabled={false}
            />
            <div className="h-24 lh-24">Execution Status</div>
            <div className="h-24 lh-24" />
        </div>
    )

    const renderInterceptedEventsList = (): JSX.Element => {
        if (!interceptedEventsList || interceptedEventsList.total === 0) {
            return renderEmptyState()
        }

        return (
            <div className="flexbox-col flex-grow-1 dc__overflow-auto">
                {renderListHeader()}
                {renderListBody()}
            </div>
        )
    }

    if (loading || clusterListLoading) {
        return <Progressing pageLoader />
    }
    if (error?.code > 0) {
        return (
            <div className="error-screen-wrapper flex column h-100">
                <ErrorScreenManager code={error.code} reload={reload} />
            </div>
        )
    }
    return (
        <>
            <div className="watch-event-list bg__primary flexbox-col flex-grow-1 dc__content-space dc__overflow-auto">
                <div className="flexbox-col flex-grow-1 dc__overflow-auto">
                    <InterceptedEventFilters
                        filterConfig={filterConfig}
                        filterCallbacks={{ updateSearchParams, handleSearch, clearFilters }}
                        hideFilters={!interceptedEventsList?.total && !areFiltersApplied}
                        clusterList={clusterListResponse}
                    />
                    {renderInterceptedEventsList()}
                </div>
                {interceptedEventsList.total > DEFAULT_BASE_PAGE_SIZE && (
                    <Pagination
                        rootClassName="flex dc__content-space pl-20 pr-20 dc__border-top"
                        size={interceptedEventsList.total}
                        offset={offset}
                        pageSize={pageSize}
                        changePage={changePage}
                        changePageSize={changePageSize}
                    />
                )}
            </div>
            {renderEventDetailModal()}
        </>
    )
}

export default InterceptedEventsList
