/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useState } from 'react'

import {
    Button,
    ButtonStyleType,
    ButtonVariantType,
    ComponentSizeType,
    noop,
    WorkflowOptionsModal,
} from '@devtron-labs/devtron-fe-common-lib'

import { LINKED_CD_SOURCE_VARIANT } from '@Common/Constants'

import { ChangeGitSourceModal } from './ChangeGitSource'
import { SyncWithCDModal } from './SyncWithCD'
import { ChangeImageSourceProps, CIPipelineType } from './types'

const ChangeImageSource = ({ selectedWorkflows, handleCloseChangeImageSource }: ChangeImageSourceProps) => {
    const [showWorkflowOptionsModal, setShowWorkflowOptionsModal] = useState<boolean>(false)
    const [changeCIType, setChangeCIType] = useState<CIPipelineType.CI_BUILD | CIPipelineType.LINKED_CD>(null)

    const handleOpenWorkflowOptionsModal = () => {
        setShowWorkflowOptionsModal(true)
    }

    const handleCloseWorkflowOptionsModal = () => {
        setShowWorkflowOptionsModal(false)
    }

    const handleOpenChangeCIModal = () => {
        setShowWorkflowOptionsModal(false)
        setChangeCIType(CIPipelineType.CI_BUILD)
    }

    const handleCloseChangeCIModal = (refreshData?: boolean) => {
        setChangeCIType(null)
        if (refreshData) {
            handleCloseChangeImageSource()
        }
    }

    const handleOpenSyncWithCDModal = () => {
        setShowWorkflowOptionsModal(false)
        setChangeCIType(CIPipelineType.LINKED_CD)
    }

    return (
        <>
            <Button
                dataTestId="change-ci-bulk"
                text="Change image source"
                onClick={handleOpenWorkflowOptionsModal}
                size={ComponentSizeType.medium}
                style={ButtonStyleType.neutral}
                variant={ButtonVariantType.secondary}
            />
            <WorkflowOptionsModal
                open={showWorkflowOptionsModal}
                onClose={handleCloseWorkflowOptionsModal}
                showLinkedCDSource
                addCIPipeline={handleOpenChangeCIModal}
                addLinkedCD={handleOpenSyncWithCDModal}
                addWebhookCD={noop}
                linkedCDSourceVariant={LINKED_CD_SOURCE_VARIANT}
                changeCIPayload={null}
                resetChangeCIPayload={noop}
                isAppGroup
                isTemplateView={false}
            />
            {changeCIType === CIPipelineType.CI_BUILD && (
                <ChangeGitSourceModal
                    selectedWorkflows={selectedWorkflows}
                    handleCloseChangeCIModal={handleCloseChangeCIModal}
                />
            )}
            {changeCIType === CIPipelineType.LINKED_CD && (
                <SyncWithCDModal
                    selectedWorkflows={selectedWorkflows}
                    handleCloseChangeCIModal={handleCloseChangeCIModal}
                />
            )}
        </>
    )
}

export default ChangeImageSource
