import { useEffect, useMemo, useRef } from 'react'

import {
    AnimatedDeployButton,
    ComponentSizeType,
    DeploymentStrategyType,
    ErrorScreenManager,
    GenericEmptyState,
    getSelectPickerOptionByValue,
    Icon,
    noop,
    PipelineIdsVsDeploymentStrategyMap,
    SegmentedBarChart,
    SelectPicker,
    SelectPickerOptionType,
    SelectPickerVariantType,
    ServerErrors,
    SortableTableHeaderCell,
    STRATEGY_TYPE_TO_TITLE_MAP,
    stringComparatorBySortOrder,
    useAsync,
    useStateFilters,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as MechanicalOperation } from '@Images/ic-mechanical-operation.svg'
import { isStrategyNotFoundError } from '@Pages/App/AppDetails/DeploymentStrategy/utils'
import { getDeploymentStrategies } from '@Pages/App/Trigger/CDPipeline/service'
import { STRATEGY_TYPE_TO_ICON_MAP } from '@Pages/constants'

import { BulkCDStrategyProps, BulkCDStrategySortableKeys, SelectStrategyProps } from './types'
import { getMessageForSelectedStrategy, getPipelineVsStrategiesMap } from './utils'

import './bulkCDStrategy.scss'

const SelectStrategy = ({
    strategies,
    selectedValue,
    handleUpdateStrategy,
    error,
    reloadOptions,
}: SelectStrategyProps) => {
    if (!strategies?.length) {
        return (
            <div className="flex dc__gap-6">
                <Icon name="ic-info-filled" color="B500" />
                <span>Strategy not found</span>
            </div>
        )
    }

    const options = strategies.map((strategy) => ({
        value: strategy,
        label: STRATEGY_TYPE_TO_TITLE_MAP[strategy] ?? strategy,
        startIcon: <Icon name={STRATEGY_TYPE_TO_ICON_MAP[strategy] ?? 'ic-rocket-gear'} color={null} />,
    }))

    const handleChange = ({ value }: SelectPickerOptionType<DeploymentStrategyType>) => {
        handleUpdateStrategy(value)
    }

    return (
        <SelectPicker
            inputId="select-strategy-for-app"
            options={options}
            variant={SelectPickerVariantType.COMPACT}
            placeholder="Select strategy"
            icon={<Icon name="ic-gear" color={null} />}
            size={ComponentSizeType.medium}
            showSelectedOptionIcon
            onChange={handleChange}
            value={getSelectPickerOptionByValue(options, selectedValue, null)}
            {...(error
                ? {
                      optionListError: new ServerErrors({ code: error.code, errors: [error] }),
                      reloadOptionList: reloadOptions,
                  }
                : {})}
        />
    )
}

const BulkCDStrategy = ({
    appList,
    envName,
    onClickDeploy,
    bulkDeploymentStrategy,
    pipelineIdVsStrategyMap,
    setPipelineIdVsStrategyMap,
}: BulkCDStrategyProps) => {
    const [strategiesLoading, pipelineStrategies, strategiesError, reloadStrategies] = useAsync(() =>
        getDeploymentStrategies(appList.map((app) => app.pipelineId)),
    )

    const { sortBy, sortOrder, handleSorting } = useStateFilters<BulkCDStrategySortableKeys>({})

    const lastSortedAppListRef = useRef<typeof appList>(appList)

    const sortedAppList = useMemo(() => {
        if (!sortBy) {
            return lastSortedAppListRef.current
        }
        const sortedList = [...appList].sort((a, b) => {
            if (sortBy === 'appName') {
                return stringComparatorBySortOrder(a.appName, b.appName, sortOrder)
            }
            return stringComparatorBySortOrder(
                pipelineIdVsStrategyMap[a.pipelineId] ?? '',
                pipelineIdVsStrategyMap[b.pipelineId] ?? '',
                sortOrder,
            )
        })
        lastSortedAppListRef.current = sortedList
        return sortedList
    }, [sortBy, sortOrder])

    const pipelineVsAllowedStrategiesMap = useMemo(
        () => getPipelineVsStrategiesMap(pipelineStrategies ?? []),
        [pipelineStrategies],
    )

    useEffect(() => {
        if (pipelineStrategies) {
            const updatedMap: PipelineIdsVsDeploymentStrategyMap = {}
            pipelineStrategies.forEach(({ pipelineId, strategies, error }) => {
                const isSelectedStrategyConfigured = !!strategies.find(
                    (strategy) => strategy.deploymentTemplate === bulkDeploymentStrategy,
                )
                if (isSelectedStrategyConfigured) {
                    updatedMap[pipelineId] = bulkDeploymentStrategy
                } else if (isStrategyNotFoundError(error?.code)) {
                    // using DEFAULT strategy for custom charts
                    updatedMap[pipelineId] = 'DEFAULT'
                }
            })
            setPipelineIdVsStrategyMap(updatedMap)
            handleSorting('deploymentStrategy') // To re-trigger sorting after pipeline vs strategy map is updated
        }
    }, [pipelineStrategies])

    if (strategiesLoading) {
        return (
            <GenericEmptyState
                SvgImage={MechanicalOperation}
                title={`Checking deployment feasibility for ${appList.length} Applications on '${envName}'`}
                subTitle="It might take some time depending upon the number of applications"
            />
        )
    }

    if (strategiesError) {
        return <ErrorScreenManager code={strategiesError.code} reload={reloadStrategies} />
    }

    const getSortingHandler = (sortKey: BulkCDStrategySortableKeys) => () => handleSorting(sortKey)

    const handleUpdateStrategy = (pipelineId: number) => (value: DeploymentStrategyType) => {
        const updatedMap: PipelineIdsVsDeploymentStrategyMap = {
            ...pipelineIdVsStrategyMap,
            ...{ [pipelineId]: value },
        }
        setPipelineIdVsStrategyMap(updatedMap)
        // clear sortBy on strategy update
        handleSorting(null)
    }

    const feasibilityCount = appList.filter(
        ({ pipelineId }) =>
            !!pipelineIdVsStrategyMap[pipelineId] ||
            isStrategyNotFoundError(pipelineVsAllowedStrategiesMap[pipelineId]?.error?.code),
    ).length

    return (
        <>
            <div className="flex-grow-1 mh-0 dc__overflow-auto pb-16">
                <div className="p-20">
                    <div className="br-8 border__primary shadow__card--20">
                        <SegmentedBarChart
                            entities={[
                                { color: 'var(--G500)', label: 'Feasible', value: feasibilityCount },
                                {
                                    color: 'var(--R500)',
                                    label: 'Not feasible',
                                    value: appList.length - feasibilityCount,
                                },
                            ]}
                            labelClassName="fs-13 fw-4 lh-20 cn-9"
                            countClassName="fs-13 fw-6 lh-20 cn-9"
                            showAnimationOnBar
                            rootClassName="p-16 border__secondary--bottom"
                        />
                        <div className="flexbox dc__align-items-center dc__gap-6 px-16 py-8">
                            <Icon name="ic-info-outline" color={null} size={20} />
                            <span>Deployment will be triggered for {feasibilityCount} applications</span>
                        </div>
                    </div>
                </div>
                <div className="feasibility-row border__secondary--bottom py-8 px-20 fs-12 lh-20 cn-7 dc__position-sticky dc__top-0 bg__primary dc__zi-1">
                    <SortableTableHeaderCell
                        title="APPLICATION"
                        isSorted={sortBy === 'appName'}
                        sortOrder={sortOrder}
                        isSortable
                        triggerSorting={getSortingHandler('appName')}
                        disabled={false}
                    />
                    <SortableTableHeaderCell
                        title="DEPLOYMENT STRATEGY"
                        isSorted={sortBy === 'deploymentStrategy'}
                        sortOrder={sortOrder}
                        isSortable
                        triggerSorting={getSortingHandler('deploymentStrategy')}
                        disabled={false}
                    />
                    <SortableTableHeaderCell
                        title="MESSAGE"
                        isSorted={false}
                        sortOrder={null}
                        isSortable={false}
                        triggerSorting={noop}
                        disabled={false}
                    />
                </div>
                {sortedAppList.map(({ appName, pipelineId }) => {
                    const config = pipelineVsAllowedStrategiesMap[pipelineId]

                    const selectedStrategy = pipelineIdVsStrategyMap[pipelineId]
                    const strategyNotConfigured = selectedStrategy === 'DEFAULT' // signifies custom chart

                    return (
                        <div key={appName} className="feasibility-row py-8 px-20 fs-13 fw-4 cn-9 lh-20">
                            <span>{appName}</span>
                            {strategyNotConfigured ? (
                                <div className="flexbox dc__gap-6">
                                    <Icon name="ic-info-filled" color="B500" size={20} />
                                    Strategy not found
                                </div>
                            ) : (
                                <div className="dc__mxw-fit-content">
                                    <SelectStrategy
                                        selectedValue={selectedStrategy}
                                        strategies={config?.strategies}
                                        error={config?.error}
                                        reloadOptions={reloadStrategies}
                                        handleUpdateStrategy={handleUpdateStrategy(pipelineId)}
                                    />
                                </div>
                            )}
                            <span>
                                {getMessageForSelectedStrategy({
                                    hasError: !!config?.error,
                                    currentDeploymentStrategy: selectedStrategy,
                                    bulkDeploymentStrategy,
                                })}
                            </span>
                        </div>
                    )
                })}
            </div>
            <div className="flex dc__align-items-center dc__content-end px-20 py-16 border__secondary--top">
                <AnimatedDeployButton
                    dataTestId="cd-trigger-deploy-button"
                    text="Deploy"
                    onButtonClick={onClickDeploy}
                    startIcon={<Icon name="ic-rocket-launch" color={null} />}
                    disabled={!feasibilityCount}
                />
            </div>
        </>
    )
}

export default BulkCDStrategy
