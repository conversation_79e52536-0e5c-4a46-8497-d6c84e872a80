/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { StylesConfig } from 'react-select'

import { getCommonSelectStyle, InputFieldState } from '@devtron-labs/devtron-fe-common-lib'

import { FeasibilityStatusCodeType } from '@Shared/types'

export const DEFAULT_RELEASE_NOTE = {
    value: '',
    updatedOn: '',
    updatedBy: {
        id: 0,
        icon: false,
        name: '',
    },
}

const commonStyles = getCommonSelectStyle()

export const selectReleaseSourceStyles: StylesConfig = {
    ...commonStyles,
    control: (base, state) => ({
        ...commonStyles.control(base, state),
        width: '100%',
        height: 36,
        minHeight: 36,
    }),
}

export const RELEASE_TRACK_ERROR = 'Please select a release track'
export const INVALID_TRACK_ERROR = 'Please select a release track from the available options'
export const RELEASE_CREATED_SUCCESS = 'Release created successfully'
export const RELEASE_UPDATED_SUCCESS = 'Release updated successfully'
export const ADD = 'add'
export const DEFAULT_INPUT_STATE: InputFieldState = { value: '', error: '' }

export const DESCRIPTION_UPDATE_MSG = 'Saved Changes'

export const DEPLOYABLE_FEASIBILITY_STATUSES: FeasibilityStatusCodeType[] = [
    FeasibilityStatusCodeType.CanTrigger,
    FeasibilityStatusCodeType.CanTriggerInDeploymentWindow,
    FeasibilityStatusCodeType.ExceptionUser,
]

export const releaseDetailGAEvents = {
    SDH_SWITCH_RELEASE_ITEM_CLICKED: 'SDH_SWITCH_RELEASE_ITEM_CLICKED',
    SDH_SWITCH_TENANTS_ITEM_CLICKED: 'SDH_SWITCH_TENANTS_ITEM_CLICKED',
}
