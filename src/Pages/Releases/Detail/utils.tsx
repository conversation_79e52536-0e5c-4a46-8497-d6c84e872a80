/*
 * Copyright (c) 2024. Devtron Inc.
 */

import {
    DeploymentSourceApplicationType,
    DeployReleaseStageType,
    ReleaseOrderApplicationsType,
    ReleaseOrderType,
    ReleaseStageAppEnvironmentType,
} from '../types'
import { AppEnvListIdentifierStateType } from './Deploy/DeployTab/types'

export const getKeyForAppEnvBulkSelectionState = (
    obj: Pick<ReleaseStageAppEnvironmentType, 'appId' | 'envId'>,
): keyof AppEnvListIdentifierStateType => `${obj.appId}-${obj.envId}`

export const getValueForAppEnvBulkSelectionState = (
    appEnv: ReleaseStageAppEnvironmentType,
    levelIndex: DeployReleaseStageType['id'],
): AppEnvListIdentifierStateType[keyof AppEnvListIdentifierStateType] => ({
    appId: appEnv.appId,
    pipelineId: appEnv.pipelineId,
    envId: appEnv.envId,
    existingStages: appEnv.existingStages,
    levelIndex,
})

export const getApplicationsMapFromReleaseOrdering = (
    releaseOrdering: ReleaseOrderType[],
): Record<string, DeploymentSourceApplicationType> => {
    if (!releaseOrdering?.length) {
        return {}
    }

    const applicationsMap = {}

    releaseOrdering.forEach((releaseOrder) => {
        releaseOrder.applications.forEach((application) => {
            applicationsMap[application.name] = JSON.parse(JSON.stringify(application)) as ReleaseOrderApplicationsType
        })
    })

    return applicationsMap
}
