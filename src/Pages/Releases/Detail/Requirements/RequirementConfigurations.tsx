/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useState } from 'react'
import { generatePath, Link, useHistory, useParams, useRouteMatch } from 'react-router-dom'
import Tippy from '@tippyjs/react'

import {
    API_STATUS_CODES,
    ArtifactInfoModal,
    ArtifactInfoModalProps,
    ErrorScreenManager,
    GenericDescription,
    GenericEmptyState,
    Progressing,
    showError,
    ToastManager,
    ToastVariantType,
    useAsync,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as ICArrowRight } from '../../../../Assets/Icons/ic-arrow-right.svg'
import { ReactComponent as ICDeleteInteractive } from '../../../../Assets/Icons/ic-delete-interactive.svg'
import { ReactComponent as DevtronAppIcon } from '../../../../Assets/Icons/ic-devtron-app.svg'
import { ReactComponent as ICImageSparkles } from '../../../../Assets/Icons/ic-image-sparkles.svg'
import { ReactComponent as ICPencil } from '../../../../Assets/Icons/ic-pencil.svg'
import { ReactComponent as ICReleaseColorPrevious } from '../../../../Assets/Icons/ic-release-color-previous.svg'
import deleteAppConfirmImg from '../../../../Assets/Img/clip-delete-confirmation.png'
import { ROUTER_URLS, URLS } from '../../../../Common/Constants'
import {
    TRACK_DETAIL_SELECTED_TAB_KEY,
    TrackDetailTabsType,
} from '../../../SoftwareDistributionHub/ReleaseTrack/Detail/constants'
import { DESCRIPTION_UPDATE_MSG } from '../../constants'
import { getAppRequirementConfigurationByName, updateAppReleaseInstructions } from '../../service'
import { CommitInfoCard } from '../../Shared/CommitInfoCard'
import { ImageInfoCard } from '../../Shared/ImageInfoCard'
import { AppRequirementConfigurationType } from '../../types'
import { renderCIListHeader } from '../History'
import { defaultReleaseInstruction } from '../Overview/constants'
import { useReleaseDetail } from '../ReleaseDetailProvider'
import DeleteApplicationConfirmationModal from './DeleteApplicationConfirmationModal'
import MapTenantInstallationPanel from './MapTenantInstallationPanel'
import PreviousReleaseImageSelectionModal from './PreviousReleaseImageSelectionModal'
import { ReleaseDetailRequirementsPathParamsType, RequirementConfigurationsProps } from './types'

const RequirementConfigurations = ({
    appName,
    refetchReleaseOrdering,
    releaseOrderingResponse,
}: RequirementConfigurationsProps) => {
    const [artifactInfoModalConfig, setArtifactInfoModalConfig] = useState<Pick<
        ArtifactInfoModalProps,
        'ciArtifactId'
    > | null>(null)
    const [isPrevReleaseImgModalOpen, setIsPrevReleaseImageModalOpen] = useState(false)
    const [showDeleteAppModal, setShowDeleteAppModal] = useState(false)

    const { id, track } = useReleaseDetail()
    const history = useHistory()
    const { path } = useRouteMatch()
    const params = useParams<ReleaseDetailRequirementsPathParamsType>()

    const [isLoading, requirementConfiguration, error, reload] = useAsync(
        () => getAppRequirementConfigurationByName(id, appName),
        [id, appName],
        !!appName,
        {
            resetOnChange: false,
        },
    )

    const toggleShowDeleteAppModal = () => {
        setShowDeleteAppModal((prev) => !prev)
    }

    const handleAppDeleteSuccess = async () => {
        await refetchReleaseOrdering()
        history.push(generatePath(path, { ...params, appName: null }))
    }

    const renderAppDeleteCTAs = () => {
        const trackDetailPath = `${generatePath(ROUTER_URLS.RELEASE_TRACK_DETAIL, {
            trackName: track,
        })}?${TRACK_DETAIL_SELECTED_TAB_KEY}=${TrackDetailTabsType.manageApplication}`

        return (
            <div className="flex column dc__gap-20">
                <button
                    type="button"
                    className="cta delete flex dc__gap-8 releases__remove-from-release-btn"
                    onClick={toggleShowDeleteAppModal}
                >
                    <ICDeleteInteractive className="icon-dim-16 dc__no-shrink scr-5 dc__no-svg-fill" />
                    Remove from release
                </button>
                <Link to={trackDetailPath} target="_blank" className="anchor flex dc__gap-4 fs-13 fw-6 lh-20 cb-5">
                    Remove from Release Track
                    <ICArrowRight className="dc__no-shrink icon-dim-16 scb-5" />
                </Link>
            </div>
        )
    }

    if (!appName) {
        return (
            <GenericEmptyState
                title="Please select an application"
                subTitle="Select an application to configure the release requirements"
                classname="flex-grow-1"
            />
        )
    }

    // We don't want to show loading in case of atomic update. Example: instruction change
    if (isLoading && !requirementConfiguration) {
        return (
            <div className="flex flex-grow-1">
                <Progressing pageLoader />
            </div>
        )
    }

    if (error || requirementConfiguration?.isDeleted) {
        if (requirementConfiguration?.isDeleted) {
            return (
                <>
                    <GenericEmptyState
                        title={`'${appName}' not found`}
                        subTitle={`Application '${appName}' is not found in the system`}
                        classname="flex-grow-1"
                        image={deleteAppConfirmImg}
                        isButtonAvailable
                        renderButton={renderAppDeleteCTAs}
                    />
                    {showDeleteAppModal && (
                        <DeleteApplicationConfirmationModal
                            appName={appName}
                            handleClose={toggleShowDeleteAppModal}
                            handleSuccess={handleAppDeleteSuccess}
                        />
                    )}
                </>
            )
        }

        if (error.code === API_STATUS_CODES.NOT_FOUND) {
            return (
                <GenericEmptyState
                    title={`'${appName}' not found`}
                    subTitle={`Application '${appName}' is not available in the system`}
                    classname="flex-grow-1"
                />
            )
        }

        return (
            <div className="flex flex-grow-1">
                <ErrorScreenManager code={error.code} redirectURL={ROUTER_URLS.RELEASES} reload={reload} />
            </div>
        )
    }

    const { imageDetails, name, releaseInstructions, commitDetails } = requirementConfiguration

    const areConfigurationAvailable = !!imageDetails?.image

    const toggleIsPrevReleaseImgModal = () => {
        setIsPrevReleaseImageModalOpen((prev) => !prev)
    }

    const updateReleaseInstructionText = async (
        newReleaseInstructions: AppRequirementConfigurationType['releaseInstructions'],
    ) => {
        try {
            await updateAppReleaseInstructions({
                id,
                releaseInstructions: newReleaseInstructions,
                appName,
            })
            ToastManager.showToast({
                variant: ToastVariantType.success,
                description: DESCRIPTION_UPDATE_MSG,
            })
            await reload()
        } catch (err) {
            showError(err)
        }
    }

    const handleOpenDeploymentSourceModal = () => {
        const currentPathName = history.location.pathname
        // TODO: need to be checked
        if (currentPathName.endsWith(URLS.DEPLOYMENT_SOURCE)) {
            return
        }

        const redirectPath = generatePath(ROUTER_URLS.RELEASE_DETAIL_REQUIREMENTS_DEPLOYMENT_SOURCE, {
            ...params,
        })

        history.push(redirectPath)
    }

    const renderBuildImageButton = () => (
        <div className="flex dc__gap-20">
            <button
                type="button"
                className="w-200 h-200 dc__border-dashed--n3 br-12 flex column dc__gap-12 p-24 dc__bg-transparent dc__hover-n0"
                onClick={handleOpenDeploymentSourceModal}
            >
                <div className="p-4 h-56 w-56">
                    <ICImageSparkles className="h-100-imp w-100-imp" />
                </div>
                <span className="fs-13 fw-6 lh-20 cb-5">Select a image</span>
            </button>
            <button
                type="button"
                className="w-200 h-200 dc__border-dashed--n3 br-12 flex column dc__gap-12 p-24 dc__bg-transparent dc__hover-n0"
                onClick={toggleIsPrevReleaseImgModal}
            >
                <div className="p-4 h-56 w-56">
                    <ICReleaseColorPrevious className="h-100-imp w-100-imp" />
                </div>
                <span className="fs-13 fw-6 lh-20 cb-5">Select from previous release version</span>
            </button>
        </div>
    )

    const closeArtifactInfoModal = () => {
        setArtifactInfoModalConfig(null)
    }

    const handleOpenArtifactInfoModal = () => {
        setArtifactInfoModalConfig({
            ciArtifactId: imageDetails?.artifactId,
        })
    }

    const doesAnyCommitHashExist = commitDetails?.some((commitDetail) => commitDetail.shortHash) || false

    return (
        <div className="flexbox flex-grow-1 w-100">
            <div className="w-100 flexbox-col h-100 border__primary--right">
                <div className="flexbox dc__content-space border__secondary--bottom px-16 py-12 dc__no-shrink">
                    <div className="flex dc__content-start dc__gap-8">
                        <DevtronAppIcon className="icon-dim-24 dc__no-shrink" />
                        <h3 className="m-0 fs-13 lh-20 fw-6 cn-9 dc__truncate">{name}</h3>
                    </div>
                </div>
                {areConfigurationAvailable ? (
                    <div className="px-16 py-12 flexbox-col flex-grow-1 dc__gap-12 dc__overflow-auto">
                        <div className="flexbox dc__gap-8">
                            {doesAnyCommitHashExist && (
                                <>
                                    <div className="flexbox releases__commit-info-card">
                                        {commitDetails.map((commitDetail, index) => (
                                            <CommitInfoCard
                                                // eslint-disable-next-line react/no-array-index-key
                                                key={index}
                                                shortHash={commitDetail.shortHash}
                                                message={commitDetail.message}
                                                handleOpenArtifactInfoModal={handleOpenArtifactInfoModal}
                                            />
                                        ))}
                                    </div>

                                    <div className="flex column dc__gap-6">
                                        <div className="h-20 dc__border-dashed--n3-right" />
                                        <ICArrowRight className="icon-dim-16 dc__no-shrink scn-6" />
                                        <div className="h-20 dc__border-dashed--n3-right" />
                                    </div>
                                </>
                            )}

                            <div className="flexbox dc__gap-12">
                                <ImageInfoCard
                                    {...imageDetails}
                                    handleOpenDeploymentSourceModal={handleOpenDeploymentSourceModal}
                                    handleOpenArtifactInfoModal={handleOpenArtifactInfoModal}
                                />

                                {/* TODO: v2 add commit/image */}
                                <Tippy content="Change image" className="default-tt" arrow={false} placement="right">
                                    <button
                                        className="flex h-100 px-8 br-8 bg__primary dc__tab-focus dc__info-card dc__hover-border-b5 dc__no-shrink"
                                        type="button"
                                        aria-label="open-deployment-source-modal"
                                        onClick={handleOpenDeploymentSourceModal}
                                    >
                                        <ICPencil className="icon-dim-20 dc__no-shrink" />
                                    </button>
                                </Tippy>
                            </div>
                        </div>
                        <GenericDescription
                            text={releaseInstructions || defaultReleaseInstruction}
                            title="Instructions"
                            updateDescription={updateReleaseInstructionText}
                        />
                    </div>
                ) : (
                    <>
                        <GenericEmptyState
                            title={`Select an image for ${appName}`}
                            subTitle="Select an image to be included in this release"
                            isButtonAvailable
                            renderButton={renderBuildImageButton}
                            classname="flex-grow-1"
                            noImage
                        />
                        {isPrevReleaseImgModalOpen && (
                            <PreviousReleaseImageSelectionModal
                                handleClose={toggleIsPrevReleaseImgModal}
                                selectedAppName={appName}
                                handleSuccess={refetchReleaseOrdering}
                            />
                        )}
                    </>
                )}
            </div>

            <MapTenantInstallationPanel
                unmappedEnvironments={releaseOrderingResponse.unmappedEnvironments}
                tenantInstallationDetailsMap={releaseOrderingResponse.tenantInstallationDetailsMap}
                releaseChannelToTenantInstallationMap={releaseOrderingResponse.releaseChannelToTenantInstallationMap}
                tenantInstallationsNotSubscribedToReleaseChannel={
                    releaseOrderingResponse.tenantInstallationsNotSubscribedToReleaseChannel
                }
            />

            {artifactInfoModalConfig && (
                <ArtifactInfoModal
                    {...artifactInfoModalConfig}
                    handleClose={closeArtifactInfoModal}
                    renderCIListHeader={renderCIListHeader}
                    fetchOnlyArtifactInfo
                />
            )}
        </div>
    )
}

export default RequirementConfigurations
