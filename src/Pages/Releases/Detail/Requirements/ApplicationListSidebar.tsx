/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useState } from 'react'
import { useHistory } from 'react-router-dom'
import Tippy from '@tippyjs/react'

import {
    Button,
    ButtonStyleType,
    ButtonVariantType,
    ComponentSizeType,
    Icon,
    InfoBlock,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as AddIcon } from '../../../../Assets/Icons/ic-add.svg'
import { ReactComponent as ArrowsLeftRight } from '../../../../Assets/Icons/ic-arrows-left-right.svg'
import { ReactComponent as LockIcon } from '../../../../Assets/Icons/ic-locked.svg'
import { ROUTER_URLS } from '../../../../Common/Constants'
import ReviewChangesModal from '../../Shared/ReviewChangesModal'
import { useReleaseDetail } from '../ReleaseDetailProvider'
import ApplicationReleaseOrder from './ApplicationReleaseOrder'
import { DeleteReleaseConfirmationModal } from './DeleteReleaseConfirmationModal'
import LockUnlockRequirementModal from './LockUnlockRequirementModal'
import { ApplicationListSidebarProps } from './types'

const ApplicationListSidebar = ({
    releaseOrdering,
    appName,
    applicationCount,
    handleShowManageReleaseModal,
    reload,
    changesForReview,
}: ApplicationListSidebarProps) => {
    const history = useHistory()
    const { id, name, isLocked, track } = useReleaseDetail()
    const changesForReviewLength = changesForReview.length

    const [isDeleteReleaseModalOpen, setIsDeleteReleaseModalOpen] = useState(false)
    const [isLockUnlockModalOpen, setIsLockUnlockModalOpen] = useState(false)
    const [isReviewChangesModalOpen, setIsReviewChangesModalOpen] = useState(false)

    const toggleDeleteReleaseModal = () => {
        setIsDeleteReleaseModalOpen(!isDeleteReleaseModalOpen)
    }

    const toggleLockUnlockModal = () => {
        setIsLockUnlockModalOpen(!isLockUnlockModalOpen)
    }

    const handleDeleteReleaseSuccess = () => {
        history.push(ROUTER_URLS.RELEASES)
    }

    const toggleReviewChangesModal = () => {
        setIsReviewChangesModalOpen((prev) => !prev)
    }

    const renderInfoBarMessage = () => (
        <span className="flex column left top dc__gap-4">
            <span className="flexbox-col fs-13 fw-4 lh-20 cn-9">
                <span className="fw-6">Action required</span>
                <span>
                    {changesForReviewLength} {changesForReviewLength === 1 ? 'update' : 'updates'} pending review
                </span>
            </span>
            <button
                type="button"
                className="dc__transparent p-0 fs-13 lh-20 fw-6 cb-5"
                onClick={toggleReviewChangesModal}
            >
                Review update
            </button>
        </span>
    )

    return (
        <>
            <div className="bg__primary flexbox-col dc__content-space h-100 border__primary--right w-250 dc__no-shrink">
                <div className="flexbox-col flex-grow-1 dc__overflow-auto">
                    <div className="flex dc__content-space pl-20 border__secondary--bottom dc__gap-4 dc__no-shrink">
                        <h3 className="m-0 cn-7 fs-13 fw-6 lh-20 dc__ellipsis-right py-12">
                            {applicationCount} {applicationCount === 1 ? 'Application' : 'Applications'}
                        </h3>
                        <div className="flex dc__no-shrink">
                            <Tippy content="Manage release order" arrow={false} className="default-tt" placement="top">
                                <button
                                    type="button"
                                    className="p-14 dc__transparent flex dc__border-left--b1"
                                    aria-label="Manage release order"
                                    onClick={handleShowManageReleaseModal}
                                >
                                    <ArrowsLeftRight className="icon-dim-20 scb-5 dc__flip-90" />
                                </button>
                            </Tippy>
                            <Tippy
                                content="Add application(s) to release"
                                arrow={false}
                                className="default-tt"
                                placement="top"
                            >
                                <button
                                    type="button"
                                    className="p-14 dc__transparent flex dc__border-left--b1"
                                    aria-label="Add applications"
                                    onClick={handleShowManageReleaseModal}
                                >
                                    <AddIcon className="icon-dim-20 fcb-5" />
                                </button>
                            </Tippy>
                        </div>
                    </div>
                    <div className="flexbox-col flex-grow-1 dc__overflow-auto px-12 py-8">
                        {changesForReviewLength > 0 && (
                            <div className="flexbox-col dc__gap-8">
                                <InfoBlock description={renderInfoBarMessage()} variant="warning" />
                                {isReviewChangesModalOpen && (
                                    <ReviewChangesModal
                                        changesForReview={changesForReview}
                                        handleClose={toggleReviewChangesModal}
                                        handleSuccess={reload}
                                        trackName={track}
                                    />
                                )}
                            </div>
                        )}
                        {releaseOrdering.map((releaseOrder) => (
                            <ApplicationReleaseOrder
                                {...releaseOrder}
                                appName={appName}
                                reload={reload}
                                key={releaseOrder.id}
                            />
                        ))}
                    </div>
                </div>
                <div className="px-16 py-12 flex dc__gap-8 dc__content-space dc__no-shrink">
                    <Button
                        dataTestId="delete-release"
                        ariaLabel="Delete release"
                        onClick={toggleDeleteReleaseModal}
                        showAriaLabelInTippy={false}
                        icon={<Icon name="ic-delete" color={null} />}
                        variant={ButtonVariantType.secondary}
                        style={ButtonStyleType.negative}
                        size={ComponentSizeType.medium}
                    />
                    <Button
                        dataTestId="lock-unlock-release"
                        onClick={toggleLockUnlockModal}
                        variant={ButtonVariantType.secondary}
                        disabled={isLockUnlockModalOpen}
                        text={isLocked ? 'Unlock to edit' : 'Lock requirements'}
                        startIcon={isLocked ? null : <LockIcon />}
                        size={ComponentSizeType.medium}
                        fullWidth
                    />
                </div>
            </div>
            {isDeleteReleaseModalOpen && (
                <DeleteReleaseConfirmationModal
                    handleClose={toggleDeleteReleaseModal}
                    handleSuccess={handleDeleteReleaseSuccess}
                    id={id}
                    name={name}
                />
            )}
            {isLockUnlockModalOpen && <LockUnlockRequirementModal handleClose={toggleLockUnlockModal} />}
        </>
    )
}

export default ApplicationListSidebar
