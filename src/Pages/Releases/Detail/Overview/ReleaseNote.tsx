/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { GenericDescription, showError, ToastManager, ToastVariantType } from '@devtron-labs/devtron-fe-common-lib'

import releaseNoteImg from '@Images/release-note-null-state.webp'

import { DESCRIPTION_UPDATE_MSG } from '../../constants'
import { updateReleaseNote } from '../../service'
import { useReleaseDetail } from '../ReleaseDetailProvider'

const ReleaseNote = () => {
    const { id, note, refetchReleaseDetail } = useReleaseDetail()

    const handleUpdateReleaseNote = async (modifiedDescriptionText: string) => {
        try {
            await updateReleaseNote({
                id,
                releaseNote: modifiedDescriptionText,
            })
            ToastManager.showToast({
                variant: ToastVariantType.success,
                description: DESCRIPTION_UPDATE_MSG,
            })
            await refetchReleaseDetail()
        } catch (error) {
            showError(error)
        }
    }

    return (
        <GenericDescription
            text={note.value}
            updatedBy={note.updatedBy.name}
            updatedOn={note.updatedOn}
            updateDescription={handleUpdateReleaseNote}
            title="Release Note"
            emptyStateConfig={{
                img: releaseNoteImg,
                subtitle: (
                    <>
                        <p className="m-0">
                            Capture all new features, bug fixes, and enhancements in release notes for each software
                            version.
                        </p>
                        <p className="m-0">
                            These detailed notes ensure transparency, enable seamless collaboration across teams, and
                            serve as a comprehensive changelog for future reference.
                        </p>
                    </>
                ),
            }}
        />
    )
}

export default ReleaseNote
