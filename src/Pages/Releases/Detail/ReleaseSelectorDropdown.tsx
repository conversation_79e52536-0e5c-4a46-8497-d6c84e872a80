/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { generatePath, useHistory, useRouteMatch } from 'react-router-dom'
import { GroupBase } from 'react-select'

import {
    ContextSwitcher,
    handleAnalyticsEvent,
    OptionType,
    SelectPickerOptionType,
    useAsync,
} from '@devtron-labs/devtron-fe-common-lib'

import { releaseDetailGAEvents } from '../constants'
import { getReleaseOptionsInTrack } from '../service'
import { useReleaseDetail } from './ReleaseDetailProvider'

const ReleaseSelectorDropdown = () => {
    const history = useHistory()
    const { path, params, url } = useRouteMatch()
    const { name, version, track } = useReleaseDetail()

    const selectedValue = { label: name, value: version }
    const [isLoading, result, error, reload] = useAsync(() => getReleaseOptionsInTrack({ trackName: track }), [track])

    const getReleaseOptions = (): GroupBase<SelectPickerOptionType<string | number>>[] => [
        {
            label: 'All Releases',
            options: (result ?? []).map((release) => ({
                label: release.name,
                value: release.version,
                description: release.version,
            })),
        },
    ]

    const handleReleaseChange = (selectedOption: OptionType) => {
        if (selectedOption.value === version) return

        handleAnalyticsEvent({
            category: 'Release Selector',
            action: releaseDetailGAEvents.SDH_SWITCH_RELEASE_ITEM_CLICKED,
        })

        const selectedTab = history.location.pathname.replace(url, '').split('/')[1]
        const newUrl = generatePath(path, {
            ...params,
            releaseVersion: selectedOption.value,
        })

        history.push(`${newUrl}/${selectedTab}`)
    }

    return (
        <ContextSwitcher
            options={getReleaseOptions()}
            onChange={handleReleaseChange}
            value={selectedValue}
            inputId={`release-switcher-${track}`}
            isLoading={isLoading}
            optionListError={error}
            reloadOptionList={reload}
        />
    )
}

export default ReleaseSelectorDropdown
