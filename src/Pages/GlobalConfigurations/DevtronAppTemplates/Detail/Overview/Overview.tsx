/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { useParams } from 'react-router-dom'
import moment from 'moment'

import {
    DATE_TIME_FORMATS,
    EditableTextArea,
    GenericDescription,
    getAlphabetIcon,
    MAX_DESCRIPTION_LENGTH,
    MESSAGES,
    showError,
    ToastManager,
    ToastVariantType,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as ICDevtronApp } from '@Icons/ic-devtron-app.svg'

import { DevtronAppTemplateDetailParams } from '../../types'
import { updateAppTemplateDescription, updateAppTemplateReadme } from '../service'
import { DEFAULT_TEMPLATE_NOTE } from './constants'
import { OverviewProps } from './types'

export const Overview = ({ templateOverview, refetchTemplateDetail }: OverviewProps) => {
    // HOOKS
    const { appId: templateDbId } = useParams<DevtronAppTemplateDetailParams>()

    const handleSaveDescription = async (description: string) => {
        try {
            await updateAppTemplateDescription({ ...templateOverview, description, id: +templateDbId })
            await refetchTemplateDetail()
            ToastManager.showToast({
                variant: ToastVariantType.success,
                description: 'Successfully saved',
            })
        } catch (err) {
            showError(err)
            throw err
        }
    }

    const handleSaveNote = async (description: string) => {
        await updateAppTemplateReadme({ description, id: templateOverview.note.id, identifier: +templateDbId })
        await refetchTemplateDetail()
        ToastManager.showToast({
            variant: ToastVariantType.success,
            description: 'Saved changes',
        })
    }

    return (
        <main className="flex-grow-1 px-20 devtron-app-template-container__overview dc__overflow-auto">
            <div className="py-20 flexbox-col dc__gap-16">
                <div className="flexbox-col dc__gap-12">
                    <ICDevtronApp className="icon-dim-48 br-8" />
                    <h2 className="m-0 fs-16 lh-24 fw-7 cn-9 dc__word-break font-merriweather">
                        {templateOverview.appName}
                    </h2>
                    <EditableTextArea
                        emptyState="Write a short description for this app template"
                        placeholder="Write a short description for this app template"
                        initialText={templateOverview.description}
                        updateContent={handleSaveDescription}
                        validations={{
                            maxLength: {
                                value: MAX_DESCRIPTION_LENGTH,
                                message: MESSAGES.getMaxCharMessage(MAX_DESCRIPTION_LENGTH),
                            },
                        }}
                    />
                </div>
                <div className="divider__secondary--horizontal" />
                <div className="flexbox-col dc__gap-4">
                    <p className="m-0 fs-12 lh-20 cn-7">Created By</p>
                    <div className="flex left">
                        {getAlphabetIcon(templateOverview.createdBy)}
                        <p className="m-0 fs-13 lh-20 fw-6 cn-9">{templateOverview.createdBy}</p>
                    </div>
                </div>
                <div className="flexbox-col dc__gap-4">
                    <p className="m-0 fs-12 lh-20 cn-7">Created On</p>
                    <p className="m-0 fs-13 lh-20 fw-6 cn-9">
                        {templateOverview.createdOn
                            ? moment(templateOverview.createdOn).format(DATE_TIME_FORMATS.TWELVE_HOURS_FORMAT)
                            : '-'}
                    </p>
                </div>
            </div>
            <div className="py-20">
                <GenericDescription
                    title="Readme"
                    updateDescription={handleSaveNote}
                    text={templateOverview.note?.description || DEFAULT_TEMPLATE_NOTE}
                    updatedBy={templateOverview.note?.updatedBy}
                    updatedOn={templateOverview.note?.updatedOn}
                />
            </div>
        </main>
    )
}
