import { useEffect, useMemo, useRef, useState } from 'react'
import { generatePath, useParams } from 'react-router-dom'

import {
    abortPreviousRequests,
    ALL_NAMESPACE_OPTION,
    API_STATUS_CODES,
    BaseURLParams,
    Button,
    ButtonComponentType,
    ButtonStyleType,
    ButtonVariantType,
    ComponentSizeType,
    FiltersTypeEnum,
    GenericEmptyState,
    getIsRequestAborted,
    Icon,
    ImageType,
    InfoBlock,
    K8sResourceDetailDataType,
    LARGE_PAGE_SIZE_OPTIONS,
    Nodes,
    noop,
    PaginationEnum,
    ResourceRecommenderActionMenuProps,
    ResourceRecommenderHeaderType,
    SelectAllDialogStatus,
    ServerErrors,
    Table,
    URLS as COMMON_URLS,
    useAsync,
    useUrlFilters,
} from '@devtron-labs/devtron-fe-common-lib'

import { ReactComponent as ICPrometheusError } from '@Icons/ic-error-prometheus.svg'
import { ReactComponent as ICPersonWriting } from '@Icons/ic-person-writing.svg'

import { RBBulkOperations } from '../BulkOperations'
import BulkSelectionActions from '../BulkOperations/BulkSelectionActions'
import { RESOURCE_RECOMMENDER_HEADER_TO_TITLE_MAP, RESOURCE_RECOMMENDER_HEADER_TO_WIDTH_MAP } from './constants'
import ResourceRecommenderTableCellComponent from './ResourceRecommenderTableCellComponent'
import {
    getResourceRecommendationList,
    getResourceRecommenderDetails,
    triggerResourceRecommenderScan,
} from './services'
import {
    ResourceRecommenderProps,
    ResourceRecommenderTableAdditionalPropsType,
    ResourceRecommenderTableProps,
} from './types'
import { parseSearchParams, tableFilter } from './utils'

const ResourceRecommender = ({
    selectedCluster,
    dynamicSort,
    ResourceRecommenderTableViewWrapper,
}: ResourceRecommenderProps) => {
    const resourceListAbortControllerRef = useRef<AbortController>(new AbortController())
    const { clusterId } = useParams<Pick<BaseURLParams, 'clusterId'>>()
    const [showAbsoluteValuesInResourceRecommender, setShowAbsoluteValuesInResourceRecommender] = useState(false)
    const [showIsExperimentalInfoBlock, setShowIsExperimentalInfoBlock] = useState(true)
    const [isResourceRecommendationScanInitiating, setIsResourceRecommendationScanInitiating] = useState<boolean>(false)

    const { selectedAPIVersionGVKFilter, selectedKindGVKFilter, selectedNamespace } = useUrlFilters({
        parseSearchParams,
    })

    const getResourceListWrapper = () =>
        abortPreviousRequests(
            () =>
                getResourceRecommendationList({
                    clusterId: +clusterId,
                    namespace: selectedNamespace === ALL_NAMESPACE_OPTION.value ? null : selectedNamespace,
                    apiVersion: selectedAPIVersionGVKFilter,
                    kind: selectedKindGVKFilter,
                    resourceListAbortControllerRef,
                }),
            resourceListAbortControllerRef,
        )

    const [
        isResourceListLoadingWithoutAbortError,
        resourceDetails,
        resourceListErrorWithAbortError,
        reloadResourceList,
    ] = useAsync(
        getResourceListWrapper,
        [clusterId, selectedNamespace, selectedKindGVKFilter, selectedAPIVersionGVKFilter],
        !!clusterId,
    )

    useEffect(
        () => () => {
            resourceListAbortControllerRef.current.abort()
        },
        [],
    )

    const isResourceListLoading =
        isResourceListLoadingWithoutAbortError || getIsRequestAborted(resourceListErrorWithAbortError)

    const resourceListError =
        !getIsRequestAborted(resourceListErrorWithAbortError) && !isResourceListLoading
            ? resourceListErrorWithAbortError
            : null

    const [areGVKOptionsLoading, recommendationDetails, gvkOptionsError, reloadGVKOptions] = useAsync(
        () => getResourceRecommenderDetails(+clusterId),
        [clusterId],
        !!clusterId,
    )

    const gvkOptions = recommendationDetails?.gvkList || []

    const { resourceList } = resourceDetails || {}

    const columns = useMemo<ResourceRecommenderTableProps['columns']>(
        () =>
            resourceList?.headers.map(
                (header: ResourceRecommenderHeaderType) =>
                    ({
                        field: header,
                        label: RESOURCE_RECOMMENDER_HEADER_TO_TITLE_MAP[header],
                        size: {
                            range: {
                                maxWidth: 600,
                                minWidth: 70,
                                startWidth: RESOURCE_RECOMMENDER_HEADER_TO_WIDTH_MAP[header],
                            },
                        },
                        CellComponent: ResourceRecommenderTableCellComponent,
                        comparator: dynamicSort(header),
                        isSortable: true,
                        horizontallySticky: header === ResourceRecommenderHeaderType.NAME,
                    }) as ResourceRecommenderTableProps['columns'][0],
            ) ?? [],
        [resourceDetails],
    )

    const rows = useMemo<ResourceRecommenderTableProps['rows']>(
        () =>
            (resourceList?.data || []).map((row) => ({
                data: row,
                id: row.id as string,
            })),
        [resourceDetails],
    )

    const initiateResourceRecommenderScan = async () => {
        setIsResourceRecommendationScanInitiating(true)
        await triggerResourceRecommenderScan(+clusterId)
        setIsResourceRecommendationScanInitiating(false)
    }

    const renderConfigurePrometheusButton = () => (
        <Button
            dataTestId="configure-prometheus-button"
            text="Configure Prometheus"
            endIcon={<Icon name="ic-arrow-right" color={null} />}
            component={ButtonComponentType.link}
            linkProps={{
                to: generatePath(COMMON_URLS.GLOBAL_CONFIG_EDIT_CLUSTER, { clusterId }),
                target: '_blank',
                rel: 'noopener noreferrer',
            }}
        />
    )

    const renderInitiateScanButton = () => (
        <Button
            dataTestId="initiate-resource-recommender-scan-button"
            text="Check for recommendations"
            startIcon={<Icon name="ic-arrows-clockwise" color={null} />}
            size={ComponentSizeType.large}
            onClick={initiateResourceRecommenderScan}
            isLoading={isResourceRecommendationScanInitiating}
        />
    )

    const handleCloseExperimentalInfoBlock = () => {
        setShowIsExperimentalInfoBlock(false)
    }

    const renderContent = () => {
        if ((resourceListError as ServerErrors)?.code === API_STATUS_CODES.PRE_CONDITION_FAILED) {
            return (
                <GenericEmptyState
                    title="Set up Prometheus to monitor this cluster"
                    subTitle="Configure Prometheus to analyze resource usage and recommend resource configurations for this cluster"
                    isButtonAvailable
                    renderButton={renderConfigurePrometheusButton}
                    SvgImage={ICPrometheusError}
                    imageType={ImageType.Large}
                />
            )
        }

        if (recommendationDetails && !recommendationDetails.lastScannedOn) {
            return (
                <GenericEmptyState
                    title="No recommendations yet"
                    subTitle="We haven’t collected any data for recommendations in this cluster yet. Start a request to analyze usage and generate helpful configuration suggestions."
                    SvgImage={ICPersonWriting}
                    imageType={ImageType.Large}
                    isButtonAvailable
                    renderButton={renderInitiateScanButton}
                />
            )
        }

        return (
            <>
                {showIsExperimentalInfoBlock && (
                    <InfoBlock
                        description="This is an experimental feature. Apply recommendations with caution."
                        size={ComponentSizeType.medium}
                        buttonProps={{
                            dataTestId: 'collapse-is-experimental-warning-button',
                            icon: <Icon name="ic-close-small" color={null} />,
                            ariaLabel: 'Close is experimental feature warning',
                            onClick: handleCloseExperimentalInfoBlock,
                            variant: ButtonVariantType.borderLess,
                            style: ButtonStyleType.neutral,
                            showAriaLabelInTippy: false,
                        }}
                        borderConfig={{
                            top: false,
                            right: false,
                            left: false,
                        }}
                        borderRadiusConfig={{
                            top: false,
                            right: false,
                            left: false,
                            bottom: false,
                        }}
                    />
                )}

                <Table<K8sResourceDetailDataType, FiltersTypeEnum.URL, ResourceRecommenderTableAdditionalPropsType>
                    columns={columns}
                    rows={rows}
                    filtersVariant={FiltersTypeEnum.URL}
                    id="table__resource-recommender"
                    paginationVariant={PaginationEnum.PAGINATED}
                    ViewWrapper={ResourceRecommenderTableViewWrapper}
                    loading={isResourceListLoading}
                    pageSizeOptions={LARGE_PAGE_SIZE_OPTIONS}
                    filter={tableFilter}
                    additionalFilterProps={{
                        initialSortKey: 'namespace',
                        parseSearchParams,
                        defaultPageSize: LARGE_PAGE_SIZE_OPTIONS[0].value,
                    }}
                    bulkSelectionConfig={{
                        BulkActionsComponent: BulkSelectionActions,
                        bulkActionsData: {
                            isResourceRecommendationView: true,
                        },
                        getSelectAllDialogStatus: () => SelectAllDialogStatus.CLOSED,
                        BulkOperationModal: RBBulkOperations,
                        bulkOperationModalData: {
                            selectedResource: {
                                gvk: {
                                    Group: '',
                                    Version: '',
                                    Kind: Nodes.ResourceRecommender,
                                },
                                namespaced: true,
                            },
                            isNodeListing: false,
                            getManifestResource: noop,
                            updateManifestResourceHelmApps: noop,
                            clusterId: +clusterId,
                            clusterName: selectedCluster?.label ?? '',
                            handleReloadDataAfterBulkOperation: reloadResourceList,
                        },
                    }}
                    emptyStateConfig={{
                        noRowsConfig: {
                            title: 'No recommendations yet',
                            subTitle:
                                'Start a request to analyze usage and generate helpful configuration suggestions.',
                            SvgImage: ICPersonWriting,
                            imageType: ImageType.Large,
                            isButtonAvailable: true,
                            renderButton: renderInitiateScanButton,
                        },
                    }}
                    additionalProps={{
                        selectedCluster,
                        selectedResource: {
                            gvk: {
                                Group: '',
                                Version: '',
                                Kind: Nodes.ResourceRecommender,
                            },
                            namespaced: true,
                        },
                        resourceListError,
                        reloadResourceList,
                        resourceRecommenderConfig: {
                            setShowAbsoluteValuesInResourceRecommender,
                            showAbsoluteValuesInResourceRecommender,
                            resourceLastScannedOnDetails: recommendationDetails
                                ? ({
                                      lastScannedOn: recommendationDetails.lastScannedOn,
                                  } satisfies Omit<ResourceRecommenderActionMenuProps, 'children'>)
                                : null,
                        },
                        gvkFilterConfig: {
                            gvkOptions,
                            areGVKOptionsLoading,
                            reloadGVKOptions,
                            gvkOptionsError,
                        },
                    }}
                />
            </>
        )
    }

    return <div className="flexbox-col dc__content-space h-100 w-100 dc__overflow-auto">{renderContent()}</div>
}

export default ResourceRecommender
