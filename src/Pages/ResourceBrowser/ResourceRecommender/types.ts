import { Dispatch, FunctionComponent, SetStateAction } from 'react'
import { GroupBase } from 'react-select'

import {
    APIOptions,
    BadgeProps,
    FiltersTypeEnum,
    GVKOptionValueType,
    GVKType,
    IconsProps,
    K8sResourceDetailDataType,
    K8sResourceDetailType,
    OptionType,
    ResourceRecommenderActionMenuProps,
    ResourceRecommenderHeaderType,
    ResourceRecommenderHeaderWithRecommendation,
    ResourceRecommenderHeaderWithStringValue,
    SelectPickerOptionType,
    TableCellComponentProps,
    TableProps,
    TableViewWrapperProps,
} from '@devtron-labs/devtron-fe-common-lib'

export interface ResourceRecommendationListPayloadType {
    clusterId: number
    k8sRequest?: {
        resourceIdentifier: {
            groupVersionKind?: GVKType
            namespace?: string
        }
    }
}

export interface ResourceRecommendationListServiceParamsType {
    clusterId: number
    namespace?: string
    apiVersion?: string
    kind?: string
    resourceListAbortControllerRef?: APIOptions['abortControllerRef']
}

export interface ResourceRecommendationResponseType {
    resourceList: K8sResourceDetailType
}

type ResourceInfoType = K8sResourceDetailDataType['additionalMetadata'][ResourceRecommenderHeaderWithRecommendation]

interface ResourceRecommenderDataDTO
    extends Record<ResourceRecommenderHeaderWithStringValue, string>,
        Record<ResourceRecommenderHeaderWithRecommendation, ResourceInfoType> {}

export interface ResourceRecommendationListDTO {
    headers: ResourceRecommenderHeaderType[]
    data: ResourceRecommenderDataDTO[]
}

export interface ApplyResourceRecommendationModalProps {
    handleClose: () => void
    handleReloadDataAfterBulkOperation: () => void
    clusterId: number
    resourceList: K8sResourceDetailDataType[]
}

export interface ResourceRecommendationChipProps {
    resourceInfo: ResourceInfoType
    columnName: ResourceRecommenderHeaderWithRecommendation
    showAbsoluteValuesInResourceRecommender: boolean
}

export interface GetRecommendationChipIconAndClassProps extends ResourceInfoType {
    /**
     * @default false
     */
    isTooltipView?: boolean
}

export interface GetRecommendationChipIconAndClassReturnType {
    class: string
    badgeProps: Pick<BadgeProps, 'variant' | 'bgColor' | 'fontColor'>
    iconProps: IconsProps
}

export type ResourceToManifestMapType = Record<
    string,
    {
        currentManifest: string
        recommendedManifest: string
    } | null
>

export interface ResourceRecommenderDetailsDTO {
    lastScannedOn?: string
    supportedGVKs: GVKType[]
}

export type ResourceRecommenderTableAdditionalPropsType = {
    selectedCluster: OptionType
    selectedResource: {
        gvk: GVKType
        namespaced: boolean
    }
    resourceListError: unknown
    reloadResourceList: () => void
    gvkFilterConfig: {
        gvkOptions: GroupBase<SelectPickerOptionType<GVKOptionValueType>>[]
        areGVKOptionsLoading: boolean
        reloadGVKOptions: () => void
        gvkOptionsError: unknown
    }
    resourceRecommenderConfig: {
        showAbsoluteValuesInResourceRecommender: boolean
        setShowAbsoluteValuesInResourceRecommender: Dispatch<SetStateAction<boolean>>
        resourceLastScannedOnDetails: Omit<ResourceRecommenderActionMenuProps, 'children'>
    }
}

export type ResourceRecommenderTableProps = TableProps<
    K8sResourceDetailDataType,
    FiltersTypeEnum.URL,
    ResourceRecommenderTableAdditionalPropsType
>

export type ResourceRecommenderTableCellComponentProps = TableCellComponentProps<
    K8sResourceDetailDataType,
    FiltersTypeEnum.URL,
    ResourceRecommenderTableAdditionalPropsType
> & {
    handleReloadDataAfterBulkOperation: () => void
}

export interface ResourceRecommenderProps {
    selectedCluster: OptionType
    dynamicSort: (field: string) => (a: Record<string, unknown>, b: Record<string, unknown>) => number
    ResourceRecommenderTableViewWrapper: FunctionComponent<
        TableViewWrapperProps<
            K8sResourceDetailDataType,
            FiltersTypeEnum.URL,
            ResourceRecommenderTableAdditionalPropsType
        >
    >
}
