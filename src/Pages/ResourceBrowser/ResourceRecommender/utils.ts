import { GroupBase } from 'react-select'

import {
    getGroupVersionFromApiVersion,
    GVK_FILTER_API_VERSION_QUERY_PARAM_KEY,
    GVK_FILTER_KIND_QUERY_PARAM_KEY,
    GVKOptionValueType,
    GVKType,
    InitTabType,
    K8sResourceDetailDataType,
    Nodes,
    ResourceRecommenderHeaderType,
    ResourceRecommenderHeaderWithRecommendation,
    ResourceRecommenderHeaderWithStringValue,
    SelectPickerOptionType,
    stringComparatorBySortOrder,
} from '@devtron-labs/devtron-fe-common-lib'

import { RESOURCE_RECOMMENDATION_CSV_HEADERS } from './constants'
import {
    GetRecommendationChipIconAndClassProps,
    GetRecommendationChipIconAndClassReturnType,
    ResourceRecommendationListDTO,
    ResourceRecommendationListPayloadType,
    ResourceRecommendationListServiceParamsType,
    ResourceRecommendationResponseType,
    ResourceRecommenderTableProps,
} from './types'

export const getResourceRecommenderTabConfig = (url: string, id: InitTabType['id']): InitTabType => ({
    id,
    name: 'Resource Recommender',
    title: 'Resource Recommendation',
    url,
    isSelected: false,
    type: 'fixed',
    showNameOnSelect: true,
    isAlive: false,
    isAlpha: true,
    defaultUrl: url,
})

export const getResourceRecommendationListPayload = ({
    clusterId,
    apiVersion,
    kind,
    namespace,
}: ResourceRecommendationListServiceParamsType): ResourceRecommendationListPayloadType => {
    const { group, version } = getGroupVersionFromApiVersion(apiVersion)
    const areGVKFilterApplied = !!(apiVersion && kind)

    return {
        clusterId,
        ...(areGVKFilterApplied || namespace
            ? {
                  k8sRequest: {
                      resourceIdentifier: {
                          ...(areGVKFilterApplied
                              ? {
                                    groupVersionKind: {
                                        Group: group,
                                        Version: version,
                                        Kind: kind as Nodes,
                                    },
                                }
                              : {}),
                          ...(namespace ? { namespace } : {}),
                      },
                  },
              }
            : {}),
    }
}

export const parseResourceRecommendationDTO = (
    result: ResourceRecommendationListDTO,
): ResourceRecommendationResponseType => {
    const headers = (result.headers || []).filter((header) => !!header)
    const { data } = result

    const filteredData = (data || []).filter(
        (item) => item.kind && item.apiVersion && item.namespace && item.containerName && item.name,
    )

    const parsedData: K8sResourceDetailDataType[] = filteredData.map((item) => {
        const additionalMetadata: K8sResourceDetailDataType['additionalMetadata'] = {
            cpuLimit: null,
            cpuRequest: null,
            memoryLimit: null,
            memoryRequest: null,
        }

        const parsedItem = {
            id: `${item.name}-${item.apiVersion}-${item.kind}-${item.namespace}-${item.containerName}`,
        } as Record<ResourceRecommenderHeaderType, string | number> &
            Record<'additionalMetadata', typeof additionalMetadata> & { id: string }

        Object.keys(item).forEach((key: ResourceRecommenderHeaderType) => {
            if (key in additionalMetadata) {
                parsedItem[key] = item[key as ResourceRecommenderHeaderWithRecommendation].delta
                additionalMetadata[key] = item[key as ResourceRecommenderHeaderWithRecommendation]

                return
            }

            parsedItem[key] = item[key as ResourceRecommenderHeaderWithStringValue] || ''
        })

        parsedItem.additionalMetadata = additionalMetadata
        return parsedItem
    })

    return {
        resourceList: {
            headers,
            data: parsedData,
        },
    }
}

export const getResourceRecommendationLabel = (value: string | 'none' | null): string => {
    if (!value) {
        return 'NA'
    }

    if (value === 'none') {
        return 'None'
    }

    return value
}

export const getResourceRecommendationsCSVData = (
    resourceList: K8sResourceDetailDataType[],
): Record<ResourceRecommenderHeaderType, string>[] =>
    (resourceList || []).map((resource) =>
        RESOURCE_RECOMMENDATION_CSV_HEADERS.reduce<Record<ResourceRecommenderHeaderType, string>>(
            (acc, { key: headerKey }) => {
                const metadata =
                    resource?.additionalMetadata?.[headerKey as ResourceRecommenderHeaderWithRecommendation]

                acc[headerKey] = metadata
                    ? `${getResourceRecommendationLabel(metadata.current?.value)} -> ${getResourceRecommendationLabel(metadata.recommended?.value)} ${metadata.delta ? `(${metadata.delta})%` : ''}`
                    : (resource?.[headerKey] as string) || ''

                return acc
            },
            {} as Record<ResourceRecommenderHeaderType, string>,
        ),
    )

export const getRecommendationChipIconAndClass = ({
    delta,
    recommended,
    current,
    isTooltipView = false,
}: GetRecommendationChipIconAndClassProps): GetRecommendationChipIconAndClassReturnType => {
    const recommendedValue = recommended?.value
    const currentValue = current?.value

    const isRecommendedValueAbsent = !recommendedValue || recommendedValue === 'none'
    const isCurrentValueAbsent = !currentValue || currentValue === 'none'

    if (delta === 0 || (isCurrentValueAbsent && isRecommendedValueAbsent)) {
        return {
            class: 'cn-3',
            badgeProps: { variant: 'custom', bgColor: 'N100', fontColor: 'N700' },
            iconProps: {
                color: isTooltipView ? 'N300' : 'N700',
                name: 'ic-minus',
            },
        }
    }

    if (delta > 0 || (!isRecommendedValueAbsent && isCurrentValueAbsent)) {
        return {
            class: 'cr-5',
            badgeProps: { variant: 'negative' },
            iconProps: {
                color: isTooltipView ? 'R500' : 'R700',
                name: 'ic-arrow-right',
                rotateBy: 270,
            },
        }
    }

    return {
        class: 'cg-5',
        badgeProps: { variant: 'positive' },
        iconProps: {
            color: isTooltipView ? 'G500' : 'G700',
            name: 'ic-arrow-right',
            rotateBy: 90,
        },
    }
}

export const parseRecommendationGVKListDTO = (
    result: GVKType[],
): GroupBase<SelectPickerOptionType<GVKOptionValueType>>[] => {
    const resultList = (result || []).map<GVKOptionValueType>((item) => ({
        kind: item.Kind,
        apiVersion: item.Group ? `${item.Group}/${item.Version}` : item.Version,
    }))

    const apiVersionToOptionsMap: Record<string, GVKOptionValueType[]> = {}
    resultList.forEach(({ apiVersion, kind }) => {
        if (!apiVersionToOptionsMap[apiVersion]) {
            apiVersionToOptionsMap[apiVersion] = []
        }
        apiVersionToOptionsMap[apiVersion].push({
            kind,
            apiVersion,
        })
    })

    const sortedApiVersionList = Object.keys(apiVersionToOptionsMap).sort(stringComparatorBySortOrder)

    return [
        {
            options: [
                {
                    label: 'All Kinds',
                    value: null,
                },
            ],
        },
        ...sortedApiVersionList.map((apiVersion) => ({
            label: apiVersion,
            options: apiVersionToOptionsMap[apiVersion]
                .map((option) => ({
                    label: option.kind,
                    value: {
                        apiVersion: option.apiVersion,
                        kind: option.kind,
                    },
                }))
                .sort((a, b) => stringComparatorBySortOrder(a.label, b.label)),
        })),
    ]
}

export const parseSearchParams = (searchParams: URLSearchParams) => ({
    ...(searchParams.has(GVK_FILTER_KIND_QUERY_PARAM_KEY)
        ? {
              selectedKindGVKFilter: searchParams.get(GVK_FILTER_KIND_QUERY_PARAM_KEY),
          }
        : {}),
    ...(searchParams.has(GVK_FILTER_API_VERSION_QUERY_PARAM_KEY)
        ? {
              selectedAPIVersionGVKFilter: searchParams.get(GVK_FILTER_API_VERSION_QUERY_PARAM_KEY),
          }
        : {}),
    ...(searchParams.has('namespace') ? { selectedNamespace: searchParams.get('namespace') } : {}),
})

export const tableFilter: ResourceRecommenderTableProps['filter'] = (row, filterData) =>
    !filterData.searchKey ||
    Object.entries(row.data).some(
        ([key, value]) =>
            key !== 'id' &&
            value !== null &&
            value !== undefined &&
            String(value).toLowerCase().includes(filterData.searchKey.toLowerCase()),
    )
