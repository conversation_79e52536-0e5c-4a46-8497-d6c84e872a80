/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { generatePath, useHistory, useRouteMatch } from 'react-router-dom'

import { ContextSwitcher, handleAnalyticsEvent, OptionType, useAsync } from '@devtron-labs/devtron-fe-common-lib'

import { releaseDetailGAEvents } from '@Pages/Releases/constants'

import { ROUTER_URLS } from '../../../Common/Constants'
import { getTenantsListOptions } from '../service'
import { useTenantDetail } from './TenantDetailProvider'

// TODO: Common out with ReleaseSelectorDropdown
const TenantSelectorDropdown = () => {
    const history = useHistory()
    const { params, url } = useRouteMatch()
    const { tenantId, name } = useTenantDetail()

    const selectedValue = { label: name, value: tenantId }

    const handleTenantChange = (selectedOption: OptionType) => {
        if (selectedOption.value === tenantId) return

        const selectedTab = history.location.pathname.replace(url, '').split('/')[1]
        const newUrl = generatePath(ROUTER_URLS.TENANT_DETAIL, {
            ...params,
            tenantId: selectedOption.value,
        })

        handleAnalyticsEvent({
            category: 'Tenant Selector',
            action: releaseDetailGAEvents.SDH_SWITCH_TENANTS_ITEM_CLICKED,
        })

        history.push(`${newUrl}/${selectedTab}`)
    }

    const [isLoading, tenantListOptions, error, reload] = useAsync(() => getTenantsListOptions(), [tenantId])

    return (
        <ContextSwitcher
            options={tenantListOptions ?? []}
            onChange={handleTenantChange}
            value={selectedValue}
            inputId={`tenant-switcher-${tenantId}`}
            isLoading={isLoading}
            optionListError={error}
            reloadOptionList={reload}
        />
    )
}

export default TenantSelectorDropdown
