/*
 * Copyright (c) 2024. Devtron Inc.
 */

import {
    GenericDescription,
    ResourceKindType,
    ResourceVersionType,
    showError,
    ToastManager,
    ToastVariantType,
} from '@devtron-labs/devtron-fe-common-lib'

import { Catalog } from '../../../../Components/Catalog'
import { DESCRIPTION_UPDATE_MSG } from '../../../Releases/constants'
import { updateTenantNote } from '../../service'
import { useTenantDetail } from '../TenantDetailProvider'
import { defaultTenantNote } from './constants'
import Sidebar from './Sidebar'

const Overview = () => {
    const { tenantId, note, refetchTenantDetail } = useTenantDetail()

    const handleUpdateTenantNote = async (modifiedDescriptionText: string) => {
        try {
            await updateTenantNote({
                tenantId,
                note: modifiedDescriptionText,
            })
            ToastManager.showToast({
                variant: ToastVariantType.success,
                description: DESCRIPTION_UPDATE_MSG,
            })
            await refetchTenantDetail()
        } catch (error) {
            showError(error)
        }
    }

    return (
        <div className="pl-20 pr-20 pt-20 pb-20 software-hub__overview display-grid dc__gap-32 dc__content-center flex-grow-1 dc__overflow-auto">
            <Sidebar />
            <div>
                <Catalog
                    resourceType={ResourceKindType.tenant}
                    resourceIdentifier={tenantId}
                    version={ResourceVersionType.alpha1}
                />
                <GenericDescription
                    text={note || defaultTenantNote}
                    title="Readme"
                    updateDescription={handleUpdateTenantNote}
                />
            </div>
        </div>
    )
}

export default Overview
