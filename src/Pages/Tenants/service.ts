/*
 * Copyright (c) 2024. Devtron Inc.
 */

import { GroupBase } from 'react-select'

import {
    ClusterType,
    ERROR_STATUS_CODE,
    get,
    getIsRequestAborted,
    getResourceApiUrl,
    patch,
    PatchOperationType,
    PatchQueryType,
    post,
    put,
    ResourceKindType,
    ResourceVersionType,
    ResponseType,
    SelectPickerOptionType,
    showError,
    stringComparatorBySortOrder,
    trash,
} from '@devtron-labs/devtron-fe-common-lib'

import { ROUTES } from '../../Common/Constants'
import { TenantListFilterConfig } from './List/types'
import {
    ClusterEnvironmentType,
    CreateInstallationPayloadType,
    CreateTenantPayloadType,
    CreateUpdateInstallationParamsType,
    CreateUpdateTenantPayloadType,
    GetClusterEnvironmentsQueryParamsType,
    GetTenantListParamsType,
    InstallationClusterDTO,
    InstallationDeploymentConfigDTO,
    InstallationDetailsDTO,
    InstallationEnvironmentDTO,
    InstallationIdentifierForApiType,
    InstallationIdentifierParamsType,
    InstallationListItemDTOWrapperType,
    PatchTenantParamsType,
    TenantDTOWrapperType,
    TenantIdentifierType,
    TenantInstallationListFilterCriteriaType,
    TenantInstallationListItemType,
    TenantInstallationType,
    TenantType,
    UpdateEnvironmentMappingParamsType,
    UpdateInstallationEnvironmentPayloadType,
    UpdateInstallationNamePayloadType,
    UpdateIsolatedInstallationDeploymentConfigParamsType,
    UpdateIsolatedInstallationDeploymentConfigPayloadType,
    UpdateTenantDescriptionParamsType,
    UpdateTenantDescriptionPayloadType,
    UpdateTenantNoteParamsType,
    UpdateTenantNotePayloadType,
    UpdateTenantPayloadType,
} from './types'
import {
    getClusterEnvironmentFromEnvironmentDTO,
    getDependencyPayloadFromSelectedEnvironmentMap,
    getInstallationIdentifierForApi,
    getTenantListFilterCriteria,
    parseInstallationDetailDTOIntoInstallationDetail,
    parseInstallationListDTOIntoInstallationListItem,
    parseTenantDTOIntoTenant,
} from './utils'

export const getTenantsList = async (
    filterConfig: TenantListFilterConfig,
    signal: AbortSignal,
): Promise<TenantType[]> => {
    try {
        const url = getResourceApiUrl<GetTenantListParamsType>({
            baseUrl: ROUTES.RESOURCE_LIST,
            kind: ResourceKindType.tenant,
            version: ResourceVersionType.alpha1,
            queryParams: {
                fetchChild: true,
                filterCriteria: getTenantListFilterCriteria(filterConfig),
            },
        })
        const {
            result: { data },
        } = (await get(url, { signal })) as ResponseType<TenantDTOWrapperType>
        return data?.map(parseTenantDTOIntoTenant) ?? []
    } catch (error) {
        if (!getIsRequestAborted(error)) {
            showError(error)
        }
        throw error
    }
}

export const getTenantsListOptions = async (): Promise<GroupBase<SelectPickerOptionType<string | number>>[]> => {
    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE_LIST,
        kind: ResourceKindType.tenant,
        version: ResourceVersionType.alpha1,
    })
    const {
        result: { data },
    } = (await get(url)) as ResponseType<TenantDTOWrapperType>
    return [
        {
            label: 'All Tenants',
            options: (data ?? []).map(parseTenantDTOIntoTenant).map(({ name, tenantId }) => ({
                label: name,
                value: tenantId,
                description: tenantId,
            })),
        },
    ]
}

export const getTenantDetailByTenantId = async (tenantId: TenantType['tenantId']): Promise<TenantType> => {
    try {
        const url = getResourceApiUrl<TenantIdentifierType>({
            baseUrl: ROUTES.RESOURCE,
            kind: ResourceKindType.tenant,
            version: ResourceVersionType.alpha1,
            queryParams: {
                identifier: tenantId,
            },
        })
        const { result } = await get(url)
        return parseTenantDTOIntoTenant(result)
    } catch (error) {
        showError(error)
        throw error
    }
}

const patchTenant = (payload: PatchTenantParamsType<PatchQueryType<string>[]>) => {
    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE,
        kind: ResourceKindType.tenant,
        version: ResourceVersionType.alpha1,
    })

    return patch(url, payload)
}

export const updateTenant = (updatedTenant: CreateUpdateTenantPayloadType) => {
    const { name, description, image: icon, tenantId } = updatedTenant
    const payload: UpdateTenantPayloadType = {
        identifier: tenantId,
        query: [
            {
                op: PatchOperationType.replace,
                path: 'name',
                value: name,
            },
            {
                op: PatchOperationType.replace,
                path: 'description',
                value: description,
            },
            {
                op: PatchOperationType.replace,
                path: 'icon',
                value: icon,
            },
        ],
    }
    return patchTenant(payload)
}

export const createTenant = (updatedTenant: CreateUpdateTenantPayloadType) => {
    const { tenantId, name, description, image: icon } = updatedTenant

    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE,
        kind: ResourceKindType.tenant,
        version: ResourceVersionType.alpha1,
    })

    const payload: CreateTenantPayloadType = {
        name,
        overview: {
            icon,
            description,
            tenantId,
        },
    }

    return post(url, payload)
}

export const deleteTenantById = async (tenantId: TenantType['tenantId']) =>
    trash(
        getResourceApiUrl<TenantIdentifierType>({
            baseUrl: ROUTES.RESOURCE,
            kind: ResourceKindType.tenant,
            version: ResourceVersionType.alpha1,
            queryParams: {
                identifier: tenantId,
            },
        }),
    )

export const updateTenantDescription = async ({ tenantId, description }: UpdateTenantDescriptionParamsType) => {
    const payload: UpdateTenantDescriptionPayloadType = {
        identifier: tenantId,
        query: [
            {
                op: PatchOperationType.replace,
                path: 'description',
                value: description,
            },
        ],
    }
    return patchTenant(payload)
}

export const updateTenantNote = async ({ tenantId, note }: UpdateTenantNoteParamsType) => {
    const payload: UpdateTenantNotePayloadType = {
        identifier: tenantId,
        query: [
            {
                op: PatchOperationType.replace,
                path: 'readme',
                value: note,
            },
        ],
    }
    return patchTenant(payload)
}

export const getTenantInstallationsById = async (
    tenantId: TenantType['tenantId'],
): Promise<TenantInstallationListItemType[]> => {
    try {
        const url = getResourceApiUrl<TenantInstallationListFilterCriteriaType>({
            baseUrl: ROUTES.RESOURCE_LIST,
            kind: ResourceKindType.installation,
            version: ResourceVersionType.alpha1,
            queryParams: {
                filterCriteria: `${ResourceKindType.tenant}|identifier|${tenantId}`,
            },
        })

        const {
            result: { data },
        } = (await get(url)) as ResponseType<InstallationListItemDTOWrapperType>
        return data?.map(parseInstallationListDTOIntoInstallationListItem) ?? []
    } catch (error) {
        showError(error)
        throw error
    }
}

export const getInstallationByInstallationId = async (
    installationIdentifierParams: InstallationIdentifierParamsType,
): Promise<TenantInstallationType> => {
    const payload: InstallationIdentifierForApiType = {
        identifier: getInstallationIdentifierForApi(installationIdentifierParams),
    }

    try {
        const [installationDepRes, installationDetailRes] = (await Promise.all([
            get(
                getResourceApiUrl<InstallationIdentifierForApiType>({
                    baseUrl: ROUTES.RESOURCE_DEPENDENCIES,
                    kind: ResourceKindType.installation,
                    version: ResourceVersionType.alpha1,
                    queryParams: payload,
                }),
            ),
            get(
                getResourceApiUrl<InstallationIdentifierForApiType>({
                    baseUrl: ROUTES.RESOURCE,
                    kind: ResourceKindType.installation,
                    version: ResourceVersionType.alpha1,
                    queryParams: payload,
                }),
            ),
        ])) as [ResponseType<InstallationDetailsDTO>, ResponseType<InstallationDeploymentConfigDTO>]

        return parseInstallationDetailDTOIntoInstallationDetail(
            installationDepRes?.result,
            installationDetailRes?.result,
        )
    } catch (error) {
        if (error?.code !== ERROR_STATUS_CODE.NOT_FOUND) {
            showError(error)
        }
        throw error
    }
}

export const deleteInstallationById = async (installationIdentifierParams: InstallationIdentifierParamsType) => {
    const url = getResourceApiUrl<InstallationIdentifierForApiType>({
        baseUrl: ROUTES.RESOURCE,
        kind: ResourceKindType.installation,
        version: ResourceVersionType.alpha1,
        queryParams: {
            identifier: getInstallationIdentifierForApi(installationIdentifierParams),
        },
    })

    return trash(url)
}

export const updateInstallationNameAndReleaseChannel = async ({
    name,
    installationId,
    tenantId,
    releaseChannelId,
}: CreateUpdateInstallationParamsType) => {
    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE,
        kind: ResourceKindType.installation,
        version: ResourceVersionType.alpha1,
    })

    const payload: UpdateInstallationNamePayloadType = {
        identifier: getInstallationIdentifierForApi({
            tenantId,
            installationId,
        }),
        query: [
            {
                op: PatchOperationType.replace,
                path: 'name',
                value: name,
            },
            releaseChannelId
                ? {
                      op: PatchOperationType.replace,
                      path: 'releaseChannel',
                      value: releaseChannelId,
                  }
                : {
                      op: PatchOperationType.remove,
                      path: 'releaseChannel',
                  },
        ],
    }

    return patch(url, payload)
}

export const updateIsolatedInstallationDeploymentConfig = async ({
    tenantId,
    installationId,
    pushToRemote,
    repository,
    dockerRegistryId,
}: UpdateIsolatedInstallationDeploymentConfigParamsType) => {
    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE,
        kind: ResourceKindType.installation,
        version: ResourceVersionType.alpha1,
    })

    const payload: UpdateIsolatedInstallationDeploymentConfigPayloadType = {
        identifier: getInstallationIdentifierForApi({
            tenantId,
            installationId,
        }),
        query: [
            {
                op: PatchOperationType.replace,
                path: 'deploymentConfiguration',
                value: pushToRemote
                    ? {
                          pushToRemote,
                          repository,
                          dockerRegistryId,
                      }
                    : {
                          pushToRemote,
                          repository: null,
                          dockerRegistryId: null,
                      },
            },
        ],
    }

    return patch(url, payload)
}

export const createInstallation = async ({
    name,
    installationId,
    tenantId,
    releaseChannelId,
}: CreateUpdateInstallationParamsType) => {
    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE,
        kind: ResourceKindType.installation,
        version: ResourceVersionType.alpha1,
    })

    const payload: CreateInstallationPayloadType = {
        name,
        overview: {
            installationId,
        },
        parentConfig: {
            identifier: tenantId,
            resourceKind: ResourceKindType.tenant,
            resourceVersion: ResourceVersionType.alpha1,
        },
        subscriptionConfig: releaseChannelId
            ? {
                  resourceKind: ResourceKindType.releaseChannel,
                  resourceVersion: ResourceVersionType.alpha1,
                  identifier: releaseChannelId,
              }
            : null,
    }

    return post(url, payload)
}

export const getClusterEnvironmentsByName = async (
    clusterName: ClusterEnvironmentType['name'],
): Promise<ClusterEnvironmentType[]> => {
    try {
        const url = getResourceApiUrl<GetClusterEnvironmentsQueryParamsType>({
            baseUrl: ROUTES.RESOURCE_DEPENDENCIES_OPTIONS,
            kind: ResourceKindType.installation,
            version: ResourceVersionType.alpha1,
            queryParams: {
                filterCriteria: `${ResourceKindType.cluster}|identifier|${clusterName}`,
            },
        })
        const { result } = (await get(url)) as ResponseType<InstallationEnvironmentDTO[]>
        return result?.map(getClusterEnvironmentFromEnvironmentDTO) ?? []
    } catch (error) {
        showError(error)
        throw error
    }
}

export const getClusterList = async (): Promise<ClusterType[]> => {
    try {
        const url = getResourceApiUrl({
            baseUrl: ROUTES.RESOURCE_DEPENDENCIES_OPTIONS,
            kind: ResourceKindType.installation,
            version: ResourceVersionType.alpha1,
        })

        const { result } = (await get(url)) as ResponseType<InstallationClusterDTO[]>
        if (!result) {
            return []
        }

        return result
            .map(({ identifier, id, data }) => ({
                id,
                name: identifier,
                isVirtual: data?.isVirtualCluster ?? false,
                isProd: data?.isProd ?? false,
            }))
            .sort((a, b) => stringComparatorBySortOrder(a.name, b.name))
    } catch (error) {
        showError(error)
        throw error
    }
}

export const updateInstallationEnvironmentMapping = async ({
    tenantId,
    installationId,
    selectedEnvironmentMap,
}: UpdateEnvironmentMappingParamsType) => {
    const url = getResourceApiUrl({
        baseUrl: ROUTES.RESOURCE_DEPENDENCIES,
        kind: ResourceKindType.installation,
        version: ResourceVersionType.alpha1,
    })

    const payload: UpdateInstallationEnvironmentPayloadType = {
        identifier: getInstallationIdentifierForApi({
            tenantId,
            installationId,
        }),
        dependencies: getDependencyPayloadFromSelectedEnvironmentMap(selectedEnvironmentMap),
    }

    return put(url, payload)
}
