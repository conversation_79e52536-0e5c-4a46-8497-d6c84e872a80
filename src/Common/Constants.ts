/*
 * Copyright (c) 2024. Devtron Inc.
 */

import {
    CIPipelineNodeType,
    PolicyKindType,
    ResourceKindType,
    ResourceVersionType,
    ROUTES as CommonRoutes,
    SourceTypeCardProps,
    URLS as CommonURLS,
} from '@devtron-labs/devtron-fe-common-lib'

import { ApproverType } from '@Components/ApprovalNode/Types'

import { HeaderTextTypes, ResourceKind } from './types'

export const ROUTES = {
    TAGS: 'global-tag',
    filter: 'filter',
    BLOCKED_STATE: 'block-state',
    CLUSTER: 'cluster',
    CLUSTER_CATEGORIES: 'cluster/categories',
    virtual_cluster: 'cluster/virtual',
    virtual_env: 'env/virtual',
    CONFIG_DRAFTS: 'draft',
    CONFIG_DRAFTS_VERSION: 'draft/version',
    CONFIG_DRAFTS_COMMENTS: 'draft/version/comments',
    CONFIG_PROTECT: 'protect/v2',
    APP_OTHER_ENVIRONMENT_MIN: 'app/other-env/min',
    APP_ARTIFACT_PROMOTE: 'app/artifact/promotion-request',
    APP_ARTIFACT_PROMOTE_ENV_LIST: 'app/artifact/promotion-request/env/list',
    APP_ARTIFACT_PROMOTE_ENV_APPROVAL_META_DATA: 'app/artifact/promotion-request/env/approval-metadata',
    APP_CI_PIPELINE_RUNTIME_PARAMS: 'app/ci-pipeline/runtime-params',
    APP_APP_WF_LIST_COMPONENTS: 'app/app-wf/list-components',
    APP_IMAGE_TAGGING_LIST: 'app/image-tagging/list',
    APP_WORKFLOW_CLONE: 'app/workflow/clone',
    APPLIED_PROFILE_POLICY: `global/policy/${ResourceVersionType.alpha1}/selectors/config`,
    APPLIED_PROFILE_POLICY_FILTERS_LIST: `global/policy/${ResourceVersionType.alpha1}/selectors/filter/list`,
    POLICY_EXCEPTION: (policyKind: PolicyKindType) =>
        `global/policy/${policyKind}/${ResourceVersionType.alpha1}/exceptions`,
    NOTIFIER: 'notification',
    ENVIRONMENT_LIST_MIN: 'env/autocomplete',
    DIGEST_POLICY: 'digest-policy',
    DEPLOYMENT_TEMPLATE: 'chartref/autocomplete/chart',
    DEPLOYMENT_WINDOW_PROFILE_LIST: 'deployment-window/profile/list',
    CREATE_DEPLOYMENT_WINDOW_PROFILE: 'deployment-window/profile',
    VALIDATE_DEPLOYMENT_WINDOW_PROFILE: 'deployment-window/validate',
    DEPLOYMENT_WINDOW_APP_ENV_LIST: 'global/policy/deployment-window/app-env/list',
    DEPLOYMENT_WINDOW_BULK_APPLY: 'global/policy/deployment-window/bulk/apply',
    ARTIFACT_PROMOTION_POLICY_LIST: 'artifact-promotion/policy',
    ARTIFACT_PROMOTION_APP_ENV_LIST: 'global/policy/artifact-promotion/app-env/list',
    ARTIFACT_PROMOTION_BULK_APPLY: 'global/policy/artifact-promotion/bulk/apply',
    NOTIFIER_CHANNEL_CONFIG_APPROVE: 'notification/channel/config/approve',
    NOTIFIER_CHANNEL_DEPLOYMENT_APPROVE: 'notification/channel/deployment/approve',
    NOTIFIER_CHANNEL_IMAGE_PROMOTION_APPROVE: 'notification/channel/image-promotion/approve',
    INFRA_CONFIG_IDENTIFIER_APPLICATIONS_APPLY: 'infra-config/identifier/application/apply',
    INFRA_CONFIG_IDENTIFIER: 'infra-config/identifier',
    INFRA_CONFIG_LIST_MIN_PLATFORMS: 'infra-config/list/min/platforms',
    USER_STATUS_BULK_UPDATE: 'user/status',
    USER_GROUP: 'user-group',
    LOCK_CONFIG_SPECIFIC_CRITERIA_OPTIONS: 'app/app-env/list',
    FILTERS_CRITERIA_META_DATA: 'filters/criteria/metadata',
    DEPLOYMENT_WINDOW_OVERVIEW: 'deployment-window/overview',
    DEPLOYMENT_WINDOW_STATE: 'deployment-window/state',
    DEPLOYMENT_WINDOW_STATE_APP_GROUP: 'deployment-window/state/appgroup',
    RESOURCE: 'resource',
    RESOURCE_CLONE: 'resource/clone',
    RESOURCE_LIST: 'resource/list',
    RESOURCE_DEPENDENCIES: 'resource/dependencies',
    RESOURCE_DEPENDENCIES_OFFENDING: 'resource/dependencies/offending',
    RESOURCE_SUMMARY: 'resource/summary',
    RESOURCE_TASKS_INFO: 'resource/task/info',
    RESOURCE_TASKS_EXECUTE: 'resource/task/execute',
    RESOURCE_DEPENDENCIES_CONFIG_OPTIONS: 'resource/dependencies/config-options',
    RESOURCE_DEPENDENCIES_OPTIONS: 'resource/dependencies/options',
    K8S_POD: 'k8s/pod/',
    K8S_DOWNLOAD_POD_CONTENT: 'k8s/download-pod-content',
    WATCHER: 'scoop/k8s/watcher',
    INTERCEPTED_CHANGES: 'scoop/k8s/intercept-events',
    INTERCEPTED_CHANGES_DETAIL: 'scoop/intercept-event',
    JOB_PIPELINE_LIST: 'job/ci-pipeline/autocomplete',
    GLOBAL_VARIABLES: 'global/variables',
    GLOBAL_VARIABLES_SUMMARY: 'global/variables/summary',
    GLOBAL_VARIABLES_FILTER: 'global/variables/filter',
    SYSTEM_NETWORK_CONTROLLER_CONFIG: 'system-network-controller/config',
    SYSTEM_NETWORK_CONTROLLER_INFO: 'system-network-controller/info',
    LOCK_CONFIG_API_ROUTE: 'config/lock',
    GLOBAL_CONFIG_DEPLOYMENT_CHART_RESOURCE_DETAIL: 'deployment/template/resource-detail',
    GLOBAL_CONFIG_DEPLOYMENT_CHART_RESOURCE: 'deployment/template/resource',
    K8S_RESOURCE_SECURITY: 'k8s/resource/security',
    OFFENDING: 'offending',
    PLUGIN_POLICY_OFFENDING_DEVTRON_APP_LIST: `offending/${ResourceKindType.devtronApplication}/list`,
    API_LIST_SUFFIX: 'list',
    API_BULK_SUFFIX: 'bulk',
    MIN: 'min',
    DRIFT_MANAGED_RESOURCE: 'drift/managed-resource',
    DRIFT_MANAGED_RESOURCES: 'drift/managed-resources',
    SWITCH_TRAFFIC: 'app/switch-traffic',
    INTELLIGENCE: 'intelligence',
    PANELS: 'panels',
    PANEL: 'panel',
    CLUSTER_PRE_UPGRADE_CHECK: 'cluster/pre-upgrade-check',
    CLUSTER_PANEL_TEMPLATE: 'cluster/panel-template',
    CLUSTER_K8S_VERSIONS_LIST: 'cluster/k8s-versions-list',
    PIPELINE_DEPLOYMENT_APPROVAL_CONFIG: 'policy/deployment-approval/pipeline',
    POLICY_CONSEQUENCES: 'app/policy-consequences',
    CONFIG_OPERATIONS: 'config/operations',
    CI_PIPELINE_SWITCH: 'app/ci-pipeline/switch',
    APP_ENV_MIN: 'app/app-env/min',
    RESOURCE_TEMPLATE_OPTIONS: `${CommonRoutes.RESOURCE_TEMPLATE}/options`,
    APP_HIBERNATION_PATCH: 'app/hibernation-patch',
    USER: 'user/v2',
    LICENSE_DATA_DETAILS: 'license/data/details',
    LICENSE_DATA: 'license/data',
    INFRASTRUCTURE_CHART_METADATA: 'infrastructure/chart/metadata',
    INFRASTRUCTURE: 'infrastructure',
    CHART_CATEGORY_LIST: 'chart-category/list',
    RECOMMENDED_CHARTS_STATUS: 'app-store/deployment/installed-app/recommended-charts/status',
    CD_PIPELINE_TRIGGER_FEASIBILITY: 'app/cd-pipeline/:appId/trigger/feasibility',
    ENV: 'env',
    APP_METADATA: 'app-metadata',
    CD_PIPELINE_STRATEGIES: 'app/cd-pipeline/strategies',
    APP_STRATEGY: 'app/strategy',
    APP_STRATEGY_ROLLOUT: 'app/strategy/rollout/promote',
}

export const URLS = {
    APP: 'app',
    DETAILS: 'details',
    RESOURCE_BROWSER: '/resource-browser',
    APP_STORE_HELM: 'app-store/helm',
    MANIFEST_DOWNLOAD: 'manifest/download',
    GLOBAL_CONFIG_CLUSTER: '/global-config/cluster-env',
    TAG_LIST: '/global-config/tags',
    TAG_CREATE: '/global-config/tags/create',
    TAG_EDIT: '/global-config/tags/edit',
    PLUGINS_LIST: '/global-config/plugin-policy',
    CREATE_PLUGIN: '/global-config/plugin-policy/create',
    EDIT_PLUGIN: '/global-config/plugin-policy/edit',
    FILTER_CONDITIONS: '/global-config/filter-condition',
    FILTER_CONDITIONS_ADD: '/global-config/filter-condition/add',
    FILTER_CONDITIONS_EDIT: '/global-config/filter-condition/edit',
    AUTH_USERS: '/global-config/auth/users',
    AUTH_API_TOKEN_EDIT: '/global-config/auth/api-token/edit/:apiTokenId',
    NOTIFIER_CONFIG: '/global-config/notifier/configurations',
    CI_PIPELINE_PATCH: 'app/ci-pipeline/patch',
    CATALOG_FRAMEWORK: '/global-config/catalog-framework',
    PERMISSION_GROUPS: '/global-config/auth/groups',
    BUILD_INFRA: '/global-config/build-infra',
    BUILD_INFRA_PROFILE_CREATE: '/global-config/build-infra/profiles/create',
    BUILD_INFRA_PROFILE_EDIT: '/global-config/build-infra/profiles/edit',
    BUILD_INFRA_APPLICATIONS: '/global-config/build-infra/applications',
    DEPLOYMENT_WINDOW_WINDOWS: '/global-config/deployment-window/list',
    DEPLOYMENT_WINDOW_APPLY_TO: '/global-config/deployment-window/apply-to',
    DEPLOYMENT_WINDOW_CREATE: '/global-config/deployment-window/window/create',
    DEPLOYMENT_WINDOW_EDIT: '/global-config/deployment-window/window/edit',
    APPLICATION_GROUP: '/application-group',
    POD_RESTART: 'pod-restart',
    DEPLOYMENT_SOURCE: '/deployment-source',
    WATCHERS: 'watchers',
    INTERCEPTED_CHANGES: 'intercepted-changes',
    WORKFLOW_EDITOR: 'edit/workflow',
    CI_PIPELINE: 'ci-pipeline',
    BUILD: 'build',
    GLOBAL_CONFIG_AUTH_USER_PERMISSION: '/global-config/auth/users',
    OVERVIEW: 'overview',
    NETWORK_STATUS_CONTROLLER_CREATE: `${CommonURLS.NETWORK_STATUS_INTERFACE}/controller/create`,
    NETWORK_STATUS_CONTROLLER_EDIT: `${CommonURLS.NETWORK_STATUS_INTERFACE}/controller/edit`,
    NETWORK_STATUS_CONTROLLER_DETAILS: `${CommonURLS.NETWORK_STATUS_INTERFACE}/controller/details`,
    NETWORK_STATUS_CUSTOMIZE_COLUMNS: `${CommonURLS.NETWORK_STATUS_INTERFACE}/customize-columns`,
    K8S_RESOURCES: 'k8s-resources',
} as const

export const ROUTER_URLS = {
    SOFTWARE_DISTRIBUTION_HUB: '/software-distribution-hub',

    RELEASES: '/software-distribution-hub/releases',
    RELEASE_CREATE: '/software-distribution-hub/releases/create',
    RELEASE_EDIT: '/software-distribution-hub/releases/edit/:releaseId',
    RELEASE_DETAIL: '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion',
    RELEASE_DETAIL_OVERVIEW: '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion/overview',
    RELEASE_DETAIL_REQUIREMENTS:
        '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion/requirements/:appName?',
    RELEASE_DETAIL_REQUIREMENTS_DEPLOYMENT_SOURCE:
        '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion/requirements/:appName?/deployment-source',
    RELEASE_DETAIL_ROLLOUT_RELEASE:
        '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion/rollout-release',
    RELEASE_DETAIL_HISTORY:
        '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion/rollout-history/:appId?/:envId?/:pipelineId?/:triggerId?',
    RELEASE_DETAIL_CONFIGURATIONS:
        '/software-distribution-hub/releases/detail/:releaseTrack/:releaseVersion/configurations/:appId?/:envId?',

    RELEASE_TRACK_ADD_EDIT: '/software-distribution-hub/releases/track/:id',
    RELEASE_RELEASE_CREATE_TRACK_ADD_EDIT: '/software-distribution-hub/releases/create/track/:id',
    RELEASE_RELEASE_EDIT_TRACK_ADD_EDIT: '/software-distribution-hub/releases/edit/:releaseId/track/:id',
    RELEASE_TRACK_DETAIL: '/software-distribution-hub/releases/track/detail/:trackName',

    TENANTS: '/software-distribution-hub/tenants',
    TENANT_CREATE: '/software-distribution-hub/tenants/create',
    TENANT_EDIT: '/software-distribution-hub/tenants/edit/:tenantId',
    TENANT_DETAIL: '/software-distribution-hub/tenants/detail/:tenantId',
    TENANT_DETAIL_OVERVIEW: '/software-distribution-hub/tenants/detail/:tenantId/overview',
    TENANT_DETAIL_INSTALLATIONS: '/software-distribution-hub/tenants/detail/:tenantId/installations/:installationId?',
    TENANT_DETAIL_INSTALLATION_MAP_ENVIRONMENT:
        '/software-distribution-hub/tenants/detail/:tenantId/installations/:installationId/map-environment',

    NETWORK_STATUS_CONTROLLER_EDIT: `${URLS.NETWORK_STATUS_CONTROLLER_EDIT}/:controllerId`,
    NETWORK_STATUS_CONTROLLER_DETAILS: `${URLS.NETWORK_STATUS_CONTROLLER_DETAILS}/:controllerId`,

    LOCK_DEPLOYMENT_CONFIGURATION: '/global-config/lock-deployment-configuration',

    LOCK_CONFIGURATION_PROFILE_LIST: '/global-config/lock-deployment-configuration/profiles',
    LOCK_CONFIGURATION_PROFILE_CREATE: '/global-config/lock-deployment-configuration/profiles/create',
    LOCK_CONFIGURATION_PROFILE_EDIT: '/global-config/lock-deployment-configuration/profiles/edit/:profileName',

    LOCK_CONFIGURATION_APPLIED_PROFILE_LIST: '/global-config/lock-deployment-configuration/applied-profiles',
    LOCK_CONFIGURATION_APPLIED_PROFILE_CREATE: '/global-config/lock-deployment-configuration/applied-profiles/create',
    LOCK_CONFIGURATION_APPLIED_PROFILE_EDIT:
        '/global-config/lock-deployment-configuration/applied-profiles/edit/:appliedProfileId',

    IMAGE_PROMOTION_PROFILE_LIST: '/global-config/image-promotion/profiles',
    IMAGE_PROMOTION_PROFILE_CREATE: '/global-config/image-promotion/profiles/create',
    IMAGE_PROMOTION_PROFILE_EDIT: '/global-config/image-promotion/profiles/edit/:profileName',

    IMAGE_PROMOTION_APPLIED_PROFILE_LIST: '/global-config/image-promotion/applied-profiles',
    IMAGE_PROMOTION_APPLIED_PROFILE_CREATE: '/global-config/image-promotion/applied-profiles/create',
    IMAGE_PROMOTION_APPLIED_PROFILE_EDIT: '/global-config/image-promotion/applied-profiles/edit/:appliedProfileId',

    PLUGIN_POLICY_PROFILE_LIST: '/global-config/plugin-policy/profiles',
    PLUGIN_POLICY_PROFILE_LIST_OFFENDING_PIPELINES:
        '/global-config/plugin-policy/profiles/offending-pipelines/:profileName',
    PLUGIN_POLICY_PROFILE_CREATE: '/global-config/plugin-policy/profiles/create',
    PLUGIN_POLICY_PROFILE_EDIT: '/global-config/plugin-policy/profiles/edit/:profileName',

    PLUGIN_POLICY_APPLIED_PROFILE_LIST: '/global-config/plugin-policy/applied-profiles',
    PLUGIN_POLICY_APPLIED_PROFILE_CREATE: '/global-config/plugin-policy/applied-profiles/create',
    PLUGIN_POLICY_APPLIED_PROFILE_EDIT: '/global-config/plugin-policy/applied-profiles/edit/:appliedProfileId',

    APPROVAL_POLICY_LIST: '/global-config/approval-policy/policy',
    APPROVAL_POLICY_CREATE: '/global-config/approval-policy/policy/create',
    APPROVAL_POLICY_EDIT: '/global-config/approval-policy/policy/edit/:policyName',

    APPROVAL_POLICY_APPLIED_POLICY_LIST: '/global-config/approval-policy/applied-policy',
    APPROVAL_POLICY_APPLIED_POLICY_CREATE: '/global-config/approval-policy/applied-policy/create',
    APPROVAL_POLICY_APPLIED_POLICY_EDIT: '/global-config/approval-policy/applied-policy/edit/:appliedProfileId',
    APPROVAL_POLICY_EXCEPTION: '/global-config/approval-policy/exceptions',

    GLOBAL_CONFIG_DEPLOYMENT_CHARTS_EDIT: `${CommonURLS.GLOBAL_CONFIG_DEPLOYMENT_CHARTS_LIST}/:name/:resourceType`,
    CLUSTER_NAMESPACES: 'cluster/namespaces',
} as const

export enum TagPageType {
    create = 'create',
    edit = 'edit',
}

export const HEADER_TEXT: HeaderTextTypes = {
    CATALOG_FRAMEWORK: {
        title: 'Catalog Framework',
        description: 'The catalog framework enables you to define a schema, acting as a framework.',
        docLink: 'GLOBAL_CONFIG_CATALOG_FRAMEWORK',
    },
    FILER_CONDITION: {
        title: 'Filter Conditions',
        description: 'Configure filter conditions to allow or block events (eg. images) for deployment pipelines.',
        docLink: 'GLOBAL_CONFIG_FILTER_CONDITION',
    },
    PULL_IMAGE_DIGEST: {
        title: 'Pull Image Digest',
        description:
            'Image digest values are used to create immutable addresses for container images. If opted, images will be pulled using its digest from the container registry, ensuring both the uniqueness and integrity of the image in the environment.',
        docLink: 'GLOBAL_CONFIG_PULL_IMAGE_DIGEST',
    },
    TAGS: {
        title: 'Tags',
        description: 'Manage tags for your organization.',
        docLink: 'GLOBAL_CONFIG_TAGS',
    },
}

export const enum DeleteComponentsName {
    Cluster = 'cluster',
}

export const LINKED_CD_SOURCE_VARIANT: Pick<
    SourceTypeCardProps,
    'title' | 'subtitle' | 'type' | 'icons' | 'dataTestId'
> = {
    title: 'Sync with Environment',
    subtitle: 'Mirrors code deployed on another environment in this application. ',
    dataTestId: 'open-linked-cd-modal',
    type: CIPipelineNodeType.LINKED_CD,
    icons: [{ name: 'ic-deploy-sync', color: null }],
}

/**
 * Various available resource kinds
 */
export const RESOURCE_KINDS = {
    DEVTRON_APPLICATION: 'devtron-application',
    CD_PIPELINE: 'cd-pipeline',
    HELM_APPLICATION: 'helm-application',
    APPLICATION: 'application',
    JOB: 'job',
    CLUSTER: 'cluster',
} as const

export const RESOURCE_KINDS_DISPLAY_NAME: Record<ResourceKind, string> = {
    [RESOURCE_KINDS.DEVTRON_APPLICATION]: 'Devtron Application',
    [RESOURCE_KINDS.CD_PIPELINE]: 'CD Pipeline',
    [RESOURCE_KINDS.HELM_APPLICATION]: 'Helm Application',
    [RESOURCE_KINDS.APPLICATION]: 'Application',
    [RESOURCE_KINDS.JOB]: 'Job',
    [RESOURCE_KINDS.CLUSTER]: 'Cluster',
} as const

export const FORM_ERROR_MESSAGE = 'Please resolve the form errors before saving'

export const DATE_TIME_FORMATS = {
    TWELVE_HOURS_FORMAT: 'ddd, DD MMM YYYY, hh:mm A',
    TWELVE_HOURS_FORMAT_WITHOUT_WEEKDAY: 'DD MMM YYYY, hh:mm A',
    TWELVE_HOURS_EXPORT_FORMAT: 'DD-MMM-YYYY hh.mm A',
    DATE_FORMAT: 'DD MMM YYYY',
    DATE_FORMAT_WITH_WEEKDAY: 'ddd, DD MMM YYYY',
}

export enum FilterAppEnvKeys {
    APPLICATIONS = 'applications',
    ENVIRONMENTS = 'environments',
    POLICY_NAMES = 'policyNames',
}

// To be used as enterprise check in dashboard if no other check is available
export const isFELibAvailable = true

export const APPROVAL_TYPE_TO_TEXT_MAP: Record<
    ApproverType.Config | ApproverType.Image,
    {
        title: string
        noUsersFound: string
        notEligibleUser: string
        usersWithPermission: string
        cancelRequestTitle: string
        cancelRequestInfo: string
    }
> = {
    [ApproverType.Config]: {
        title: 'Configuration approver',
        noUsersFound: 'No users found with configuration approver permission.',
        notEligibleUser: `User does not have 'Configuration approver' permission`,
        usersWithPermission:
            'All users having ‘Configuration approver’ permission for this application and environment can approve.',
        cancelRequestTitle: 'Discard Draft',
        cancelRequestInfo: 'Are you sure you want to discard this draft? Any changes made to the draft will be lost.',
    },
    [ApproverType.Image]: {
        title: 'Image approver',
        noUsersFound: 'No users found with image approver permission.',
        notEligibleUser: `User does not have 'Image approver' permission`,
        usersWithPermission:
            'All users having ‘Image approver’ permission for this application and environment can approve.',
        cancelRequestTitle: 'Cancel request',
        cancelRequestInfo: 'Are you sure you want to cancel the request? ',
    },
} as const

export const PREVIEW_DEVTRON = 'https://preview.devtron.ai'
