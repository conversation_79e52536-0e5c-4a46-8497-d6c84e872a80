.segmented-control {
    $segmented-control-selector: &;

    background: var(--bg-segmented-control);

    &__container {
        &:has(input[type='radio']:checked:focus-visible) {
            .segmented-control__segment--selected {
                .active-mask {
                    outline: 2px solid var(--B500) !important;
                    outline-offset: 2px;
                }
            }
        }
    }

    &__segment {
        $parent-selector: &;
        transition: color 0.3s ease;

        &--selected {
            &#{$parent-selector} {

                &--xs,
                &--small {
                    padding-block: 1.5px;
                    padding-inline: 5.5px;
                }

                &--medium {
                    padding-block: 3.5px;
                    padding-inline: 7.5px;
                }
            }

            .active-mask {
                border: 0.5px solid var(--border-primary);
                box-shadow: 0px 1px 2px 0px var(--black-20);
            }
        }

        > span {
            position: relative;
            z-index: 1;
        }

        &:hover:not(#{$parent-selector}--selected):not(.cursor-not-allowed) {
            background-color: var(--bg-hover);
        }
    }
}
